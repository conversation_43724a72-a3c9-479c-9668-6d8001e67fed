"""
批量生成器
"""
import os
import time
import json
from typing import Dict, List, Optional
import logging

try:
    from ..utils import config
    from ..scene import SceneGenerator
    from ..camera import CameraController
    from ..sar import SARImaging
    from ..dataset import COCOGenerator, DatasetSplitter
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config
    from scene import SceneGenerator
    from camera import CameraController
    from sar import SARImaging
    from dataset import COCOGenerator, DatasetSplitter
from .progress_tracker import ProgressTracker

logger = logging.getLogger(__name__)


class BatchGenerator:
    """批量生成器，负责高效地批量生成SAR数据集"""
    
    def __init__(self):
        """初始化批量生成器"""
        # 初始化各个组件
        self.scene_generator = SceneGenerator()
        self.camera_controller = CameraController()
        self.sar_imaging = SARImaging()
        self.coco_generator = COCOGenerator()
        self.dataset_splitter = DatasetSplitter()
        
        # 从配置获取参数
        self.total_images = config.get('dataset.total_images', 10000)
        self.output_dir = config.get('dataset.output_dir', './output')
        self.image_size = config.get('dataset.image_size', [512, 512])
        
        # 创建输出目录结构
        self._create_output_directories()
        
        # 进度跟踪器
        self.progress_tracker = ProgressTracker(self.total_images, self.output_dir)
        
    def _create_output_directories(self):
        """创建输出目录结构"""
        directories = [
            self.output_dir,
            os.path.join(self.output_dir, 'images'),
            os.path.join(self.output_dir, 'annotations'),
            os.path.join(self.output_dir, 'scenes'),
            os.path.join(self.output_dir, 'logs'),
            os.path.join(self.output_dir, 'splits')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        logger.info(f"输出目录结构已创建: {self.output_dir}")
    
    def generate_dataset(self, resume: bool = False) -> bool:
        """
        生成完整的数据集
        
        Args:
            resume: 是否从上次中断的地方继续
            
        Returns:
            是否成功生成
        """
        try:
            # 检查是否需要恢复进度
            start_index = 0
            if resume:
                progress_data = self.progress_tracker.load_progress()
                if progress_data:
                    start_index = progress_data.get('completed_tasks', 0)
                    logger.info(f"从第 {start_index + 1} 个任务开始恢复生成")
            
            # 开始生成
            self.progress_tracker.start()
            
            # 批量生成图像和标注
            success = self._generate_images_and_annotations(start_index)
            
            if success:
                # 生成COCO数据集文件
                self._save_coco_dataset()
                
                # 分割数据集
                self._split_dataset()
                
                # 生成统计报告
                self._generate_reports()
                
                logger.info("数据集生成完成")
                self.progress_tracker.print_summary()
                return True
            else:
                logger.error("数据集生成失败")
                return False
                
        except Exception as e:
            logger.error(f"数据集生成过程中发生错误: {e}")
            return False
    
    def _generate_images_and_annotations(self, start_index: int = 0) -> bool:
        """
        批量生成图像和标注
        
        Args:
            start_index: 开始索引
            
        Returns:
            是否成功
        """
        success_count = 0
        
        for i in range(start_index, self.total_images):
            task_start_time = time.time()
            
            try:
                # 生成场景
                scene_info = self._generate_single_scene(i + 1)
                if not scene_info:
                    self.progress_tracker.update_task_failed(i + 1, "场景生成失败")
                    continue
                
                # 设置相机
                camera_success = self._setup_camera_for_scene(scene_info)
                if not camera_success:
                    self.progress_tracker.update_task_failed(i + 1, "相机设置失败")
                    continue
                
                # 生成SAR图像
                sar_image, imaging_info = self._generate_sar_image(scene_info)
                if sar_image is None:
                    self.progress_tracker.update_task_failed(i + 1, "SAR图像生成失败")
                    continue
                
                # 保存图像
                image_path = self._save_image(sar_image, i + 1)
                if not image_path:
                    self.progress_tracker.update_task_failed(i + 1, "图像保存失败")
                    continue
                
                # 添加到COCO数据集
                camera_params = self.camera_controller.get_camera_parameters()
                coco_success = self.coco_generator.add_image_and_annotations(
                    image_path, scene_info, camera_params, tuple(self.image_size)
                )
                
                # 保存场景信息（即使COCO标注失败也保存，用于调试）
                self._save_scene_info(scene_info, imaging_info, camera_params, i + 1)

                if not coco_success:
                    self.progress_tracker.update_task_failed(i + 1, "COCO标注创建失败")
                    continue
                
                # 更新进度
                task_time = time.time() - task_start_time
                task_info = {
                    'scene_id': i + 1,
                    'aircraft_count': scene_info.get('aircraft_count', 0),
                    'image_path': image_path,
                    'imaging_quality': imaging_info.get('quality_metrics', {})
                }
                
                self.progress_tracker.update_task_completed(i + 1, task_info, task_time)
                success_count += 1
                
                # 定期清理内存
                if (i + 1) % 100 == 0:
                    self._cleanup_memory()
                
            except Exception as e:
                self.progress_tracker.update_task_failed(i + 1, str(e))
                logger.error(f"生成第 {i + 1} 个样本时发生错误: {e}")
                continue
        
        logger.info(f"图像生成完成，成功: {success_count}/{self.total_images}")
        return success_count > 0
    
    def _generate_single_scene(self, scene_id: int) -> Optional[Dict]:
        """
        生成单个场景
        
        Args:
            scene_id: 场景ID
            
        Returns:
            场景信息或None
        """
        try:
            # 使用场景ID作为随机种子以确保可重现性
            scene_info = self.scene_generator.generate_scene(scene_id, seed=scene_id * 42)
            return scene_info
        except Exception as e:
            logger.error(f"生成场景 {scene_id} 失败: {e}")
            return None
    
    def _setup_camera_for_scene(self, scene_info: Dict) -> bool:
        """
        为场景设置相机
        
        Args:
            scene_info: 场景信息
            
        Returns:
            是否成功
        """
        try:
            # 创建相机
            camera_obj = self.camera_controller.create_sar_camera()
            if not camera_obj:
                return False
            
            # 根据场景边界设置相机
            scene_bounds = scene_info.get('scene_bounds', {})
            if not scene_bounds:
                return False
            
            success = self.camera_controller.setup_camera_for_scene(scene_bounds)
            if success:
                self.camera_controller.set_as_active_camera()
                self.camera_controller.setup_render_settings()
            
            return success
            
        except Exception as e:
            logger.error(f"设置相机失败: {e}")
            return False
    
    def _generate_sar_image(self, scene_info: Dict) -> tuple:
        """
        生成SAR图像
        
        Args:
            scene_info: 场景信息
            
        Returns:
            (SAR图像, 成像信息)
        """
        try:
            camera_params = self.camera_controller.get_camera_parameters()
            
            # 生成SAR图像
            sar_image, imaging_info = self.sar_imaging.simulate_sar_image_from_scene(
                scene_info, camera_params
            )
            
            return sar_image, imaging_info
            
        except Exception as e:
            logger.error(f"生成SAR图像失败: {e}")
            return None, {}
    
    def _save_image(self, image, image_id: int) -> Optional[str]:
        """
        保存图像
        
        Args:
            image: 图像数组
            image_id: 图像ID
            
        Returns:
            保存的文件路径或None
        """
        try:
            import cv2
            
            filename = f"sar_image_{image_id:06d}.png"
            filepath = os.path.join(self.output_dir, 'images', filename)
            
            # 保存图像
            cv2.imwrite(filepath, image)
            
            return filepath
            
        except Exception as e:
            logger.error(f"保存图像失败: {e}")
            return None
    
    def _save_scene_info(self, scene_info: Dict, imaging_info: Dict, camera_params: Dict, scene_id: int):
        """
        保存场景信息

        Args:
            scene_info: 场景信息
            imaging_info: 成像信息
            camera_params: 相机参数
            scene_id: 场景ID
        """
        try:
            combined_info = {
                'scene_info': scene_info,
                'imaging_info': imaging_info,
                'camera_params': camera_params
            }
            
            filename = f"scene_{scene_id:06d}.json"
            filepath = os.path.join(self.output_dir, 'scenes', filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(combined_info, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存场景信息失败: {e}")
    
    def _save_coco_dataset(self):
        """保存COCO数据集"""
        try:
            coco_file = os.path.join(self.output_dir, 'annotations', 'instances.json')
            self.coco_generator.save_coco_dataset(coco_file)
            
            # 验证数据集
            if self.coco_generator.validate_dataset():
                logger.info("COCO数据集验证通过")
            else:
                logger.warning("COCO数据集验证失败")
                
        except Exception as e:
            logger.error(f"保存COCO数据集失败: {e}")
    
    def _split_dataset(self):
        """分割数据集"""
        try:
            coco_file = os.path.join(self.output_dir, 'annotations', 'instances.json')
            splits_dir = os.path.join(self.output_dir, 'splits')
            
            # 分割数据集
            success = self.dataset_splitter.split_coco_dataset(coco_file, splits_dir)
            
            if success:
                # 复制图像文件到对应的分割目录
                images_dir = os.path.join(self.output_dir, 'images')
                coco_files = {
                    'train': os.path.join(splits_dir, 'train_annotations.json'),
                    'val': os.path.join(splits_dir, 'val_annotations.json'),
                    'test': os.path.join(splits_dir, 'test_annotations.json')
                }
                
                self.dataset_splitter.copy_images_for_split(images_dir, splits_dir, coco_files)
                
                # 验证分割平衡性
                balance_stats = self.dataset_splitter.validate_split_balance(splits_dir)
                logger.info("数据集分割完成")
                
        except Exception as e:
            logger.error(f"数据集分割失败: {e}")
    
    def _generate_reports(self):
        """生成统计报告"""
        try:
            # 生成COCO数据集统计
            coco_stats = self.coco_generator.get_dataset_statistics()
            
            # 生成进度统计
            progress_stats = self.progress_tracker.get_statistics()
            
            # 合并统计信息
            combined_stats = {
                'generation_summary': progress_stats,
                'dataset_statistics': coco_stats,
                'configuration': {
                    'total_images': self.total_images,
                    'image_size': self.image_size,
                    'output_directory': self.output_dir
                }
            }
            
            # 保存统计报告
            stats_file = os.path.join(self.output_dir, 'dataset_statistics.json')
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(combined_stats, f, indent=2, ensure_ascii=False)
            
            # 导出详细报告
            detailed_report_file = os.path.join(self.output_dir, 'detailed_report.json')
            self.progress_tracker.export_detailed_report(detailed_report_file)
            
            logger.info(f"统计报告已生成: {stats_file}")
            
        except Exception as e:
            logger.error(f"生成统计报告失败: {e}")
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            # 清理场景
            self.scene_generator.clear_scene()
            
            # 清理相机
            self.camera_controller.cleanup()
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
        except Exception as e:
            logger.error(f"内存清理失败: {e}")
    
    def resume_generation(self) -> bool:
        """
        恢复中断的生成过程
        
        Returns:
            是否成功恢复
        """
        return self.generate_dataset(resume=True)
    
    def get_generation_status(self) -> Dict:
        """
        获取生成状态
        
        Returns:
            状态信息字典
        """
        return self.progress_tracker.get_statistics()
