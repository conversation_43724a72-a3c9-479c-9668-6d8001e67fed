"""
飞机模型加载器
"""
import os
from typing import Dict, List, Optional, Tuple
import logging

try:
    import bpy
    import bmesh
    from mathutils import Vector
    BLENDER_AVAILABLE = True
except ImportError:
    # 在测试环境中模拟Blender对象
    BLENDER_AVAILABLE = False
    class MockObject:
        def __init__(self, name):
            self.name = name
            self.type = 'MESH'
    bpy = None

try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config

logger = logging.getLogger(__name__)


class AircraftLoader:
    """飞机模型加载器"""
    
    def __init__(self):
        """初始化加载器"""
        self.loaded_models = {}  # 缓存已加载的模型
        self.model_configs = config.get('aircraft.models', [])
        self.models_dir = config.get('aircraft.models_dir')

        logger.info(f"飞机加载器初始化: 模型目录={self.models_dir}, 配置数量={len(self.model_configs)}")
        for cfg in self.model_configs:
            logger.debug(f"模型配置: {cfg.get('name', 'unknown')} - {cfg.get('file', 'no file')}")
        
    def load_model(self, model_name: str):
        """
        加载指定的飞机模型

        Args:
            model_name: 模型名称

        Returns:
            加载的Blender对象，失败返回None
        """
        if not BLENDER_AVAILABLE:
            # 在测试环境中返回模拟对象
            return MockObject(model_name)

        # 查找模型配置
        model_config = None
        for cfg in self.model_configs:
            if cfg['name'] == model_name:
                model_config = cfg
                break
                
        if not model_config:
            logger.error(f"未找到模型配置: {model_name}")
            return None
            
        # 检查是否已加载
        if model_name in self.loaded_models:
            logger.debug(f"模型已缓存: {model_name}")
            return self.loaded_models[model_name]
            
        # 构建文件路径
        model_file = os.path.join(self.models_dir, model_config['file'])
        if not os.path.exists(model_file):
            logger.error(f"模型文件不存在: {model_file}")
            return None
            
        try:
            logger.info(f"开始加载模型: {model_name} from {model_file}")

            # 清除现有选择
            # 确保在正确的上下文中
            if bpy.context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # 使用更安全的方式清除选择
            for obj in bpy.context.selected_objects:
                obj.select_set(False)

            # 记录导入前的对象
            objects_before = set(bpy.context.scene.objects)

            # 导入FBX文件
            logger.debug(f"导入FBX文件: {model_file}")
            bpy.ops.import_scene.fbx(filepath=model_file)

            # 找到新导入的对象
            objects_after = set(bpy.context.scene.objects)
            new_objects = objects_after - objects_before

            logger.info(f"导入后新增对象数量: {len(new_objects)}")

            if not new_objects:
                logger.error(f"导入模型失败，未找到新对象: {model_name}")
                # 尝试创建一个简单的替代对象
                return self._create_fallback_aircraft(model_name)

            # 合并所有新对象为一个
            aircraft_obj = self._merge_objects(list(new_objects), model_name)

            if aircraft_obj:
                # 缓存模型
                self.loaded_models[model_name] = aircraft_obj
                logger.info(f"成功加载模型: {model_name}")
            else:
                logger.warning(f"合并对象失败，创建fallback对象: {model_name}")
                aircraft_obj = self._create_fallback_aircraft(model_name)

            return aircraft_obj

        except Exception as e:
            logger.error(f"加载模型失败 {model_name}: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            # 创建fallback对象
            return self._create_fallback_aircraft(model_name)
    
    def _merge_objects(self, objects, model_name: str):
        """
        合并多个对象为一个

        Args:
            objects: 要合并的对象列表
            model_name: 模型名称

        Returns:
            合并后的对象
        """
        if not BLENDER_AVAILABLE:
            return MockObject(model_name) if objects else None

        if not objects:
            return None
            
        # 过滤出网格对象
        mesh_objects = [obj for obj in objects if obj.type == 'MESH']
        
        if not mesh_objects:
            logger.warning(f"未找到网格对象: {model_name}")
            return None
            
        if len(mesh_objects) == 1:
            # 只有一个对象，直接重命名
            mesh_objects[0].name = model_name
            return mesh_objects[0]
            
        # 多个对象需要合并
        try:
            # 确保在对象模式
            if bpy.context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # 清除现有选择
            for obj in bpy.context.selected_objects:
                obj.select_set(False)

            # 选择所有网格对象
            for obj in mesh_objects:
                obj.select_set(True)

            # 设置活动对象
            bpy.context.view_layer.objects.active = mesh_objects[0]

            # 合并对象
            bpy.ops.object.join()
            
            # 重命名合并后的对象
            merged_obj = bpy.context.active_object
            merged_obj.name = model_name
            
            # 删除非网格对象
            for obj in objects:
                if obj not in mesh_objects and obj.name in bpy.data.objects:
                    bpy.data.objects.remove(obj, do_unlink=True)
                    
            return merged_obj
            
        except Exception as e:
            logger.error(f"合并对象失败 {model_name}: {e}")
            return None
    
    def get_model_dimensions(self, obj) -> Tuple[float, float, float]:
        """
        获取模型的尺寸

        Args:
            obj: Blender对象

        Returns:
            (长度, 宽度, 高度)
        """
        if not BLENDER_AVAILABLE:
            return (20.0, 15.0, 5.0)  # 模拟尺寸

        if not obj or obj.type != 'MESH':
            return (0, 0, 0)
            
        # 获取边界框
        bbox = [obj.matrix_world @ Vector(corner) for corner in obj.bound_box]
        
        # 计算各轴的尺寸
        min_x = min(v.x for v in bbox)
        max_x = max(v.x for v in bbox)
        min_y = min(v.y for v in bbox)
        max_y = max(v.y for v in bbox)
        min_z = min(v.z for v in bbox)
        max_z = max(v.z for v in bbox)
        
        length = max_x - min_x
        width = max_y - min_y
        height = max_z - min_z
        
        return (length, width, height)
    
    def get_model_config(self, model_name: str) -> Optional[Dict]:
        """
        获取模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            模型配置字典
        """
        for cfg in self.model_configs:
            if cfg['name'] == model_name:
                return cfg
        return None
    
    def list_available_models(self) -> List[str]:
        """
        获取可用的模型列表
        
        Returns:
            模型名称列表
        """
        return [cfg['name'] for cfg in self.model_configs]
    
    def _create_fallback_aircraft(self, model_name: str):
        """
        创建fallback飞机对象（当模型加载失败时使用）

        Args:
            model_name: 模型名称

        Returns:
            简单的飞机对象
        """
        if not BLENDER_AVAILABLE:
            return MockObject(model_name)

        try:
            # 确保在正确的模式下
            if bpy.context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # 清除选择
            bpy.ops.object.select_all(action='DESELECT')

            # 创建一个简单的立方体作为飞机
            bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
            aircraft_obj = bpy.context.active_object

            if not aircraft_obj:
                logger.error("无法创建立方体对象")
                return None

            aircraft_obj.name = f"Aircraft_{model_name}_fallback"

            # 调整尺寸使其看起来像飞机
            aircraft_obj.scale = (3, 1, 0.5)  # 长、宽、高比例

            # 应用变换
            bpy.context.view_layer.objects.active = aircraft_obj
            aircraft_obj.select_set(True)
            bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

            # 添加自定义属性
            aircraft_obj['is_fallback'] = True
            aircraft_obj['original_model'] = model_name

            logger.info(f"创建fallback飞机对象: {model_name}")
            return aircraft_obj

        except Exception as e:
            logger.error(f"创建fallback飞机失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None

    def clear_cache(self):
        """清除模型缓存"""
        self.loaded_models.clear()
        logger.info("模型缓存已清除")

    def duplicate_model(self, model_name: str):
        """
        复制模型实例

        Args:
            model_name: 模型名称

        Returns:
            复制的对象实例
        """
        if not BLENDER_AVAILABLE:
            return MockObject(f"{model_name}_copy")

        original = self.load_model(model_name)
        if not original:
            return None

        try:
            # 检查原始对象是否仍然有效
            if original.name not in bpy.data.objects:
                logger.error(f"原始模型对象已被删除: {model_name}")
                # 尝试重新加载
                original = self.load_model(model_name)
                if not original:
                    return None

            # 复制对象和数据
            duplicate = original.copy()
            duplicate.data = original.data.copy()

            # 生成唯一名称
            import time
            unique_suffix = f"_{int(time.time() * 1000) % 100000}"
            duplicate.name = f"{model_name}{unique_suffix}"

            # 添加到场景
            bpy.context.collection.objects.link(duplicate)

            return duplicate

        except Exception as e:
            logger.error(f"复制模型失败 {model_name}: {e}")
            return None
