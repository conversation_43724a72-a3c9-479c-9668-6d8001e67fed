"""
进度跟踪器
"""
import time
import json
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class ProgressTracker:
    """进度跟踪器，用于跟踪批量生成的进度"""
    
    def __init__(self, total_tasks: int, output_dir: str):
        """
        初始化进度跟踪器
        
        Args:
            total_tasks: 总任务数
            output_dir: 输出目录
        """
        self.total_tasks = total_tasks
        self.output_dir = output_dir
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time = None
        self.task_times = []
        
        # 进度文件路径
        self.progress_file = os.path.join(output_dir, 'progress.json')
        
        # 任务详情
        self.task_details = []
        
    def start(self):
        """开始跟踪"""
        self.start_time = time.time()
        logger.info(f"开始批量生成，总任务数: {self.total_tasks}")
        self._save_progress()
    
    def update_task_completed(self, task_id: int, task_info: Dict, 
                            processing_time: float):
        """
        更新任务完成状态
        
        Args:
            task_id: 任务ID
            task_info: 任务信息
            processing_time: 处理时间（秒）
        """
        self.completed_tasks += 1
        self.task_times.append(processing_time)
        
        # 记录任务详情
        task_detail = {
            'task_id': task_id,
            'status': 'completed',
            'processing_time': processing_time,
            'timestamp': datetime.now().isoformat(),
            'info': task_info
        }
        self.task_details.append(task_detail)
        
        # 计算进度
        progress = self.completed_tasks / self.total_tasks * 100
        
        # 估算剩余时间
        remaining_time = self._estimate_remaining_time()
        
        logger.info(f"任务 {task_id} 完成 ({self.completed_tasks}/{self.total_tasks}, "
                   f"{progress:.1f}%, 剩余时间: {remaining_time})")
        
        self._save_progress()
    
    def update_task_failed(self, task_id: int, error_message: str):
        """
        更新任务失败状态
        
        Args:
            task_id: 任务ID
            error_message: 错误信息
        """
        self.failed_tasks += 1
        
        # 记录任务详情
        task_detail = {
            'task_id': task_id,
            'status': 'failed',
            'error': error_message,
            'timestamp': datetime.now().isoformat()
        }
        self.task_details.append(task_detail)
        
        logger.error(f"任务 {task_id} 失败: {error_message}")
        self._save_progress()
    
    def _estimate_remaining_time(self) -> str:
        """
        估算剩余时间
        
        Returns:
            剩余时间字符串
        """
        if not self.task_times:
            return "未知"
        
        # 计算平均处理时间
        avg_time = sum(self.task_times) / len(self.task_times)
        
        # 计算剩余任务数
        remaining_tasks = self.total_tasks - self.completed_tasks - self.failed_tasks
        
        # 估算剩余时间
        remaining_seconds = remaining_tasks * avg_time
        
        if remaining_seconds < 60:
            return f"{remaining_seconds:.0f}秒"
        elif remaining_seconds < 3600:
            return f"{remaining_seconds/60:.1f}分钟"
        else:
            return f"{remaining_seconds/3600:.1f}小时"
    
    def _save_progress(self):
        """保存进度到文件"""
        try:
            current_time = time.time()
            elapsed_time = current_time - self.start_time if self.start_time else 0
            
            progress_data = {
                'total_tasks': self.total_tasks,
                'completed_tasks': self.completed_tasks,
                'failed_tasks': self.failed_tasks,
                'progress_percentage': self.completed_tasks / self.total_tasks * 100,
                'start_time': self.start_time,
                'elapsed_time': elapsed_time,
                'estimated_remaining_time': self._estimate_remaining_time(),
                'average_task_time': sum(self.task_times) / len(self.task_times) if self.task_times else 0,
                'last_updated': current_time,
                'task_details': self.task_details[-10:]  # 只保存最近10个任务的详情
            }
            
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存进度文件失败: {e}")
    
    def load_progress(self) -> Optional[Dict]:
        """
        加载之前的进度
        
        Returns:
            进度数据或None
        """
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                
                # 恢复状态
                self.completed_tasks = progress_data.get('completed_tasks', 0)
                self.failed_tasks = progress_data.get('failed_tasks', 0)
                self.start_time = progress_data.get('start_time')
                
                logger.info(f"加载进度: {self.completed_tasks}/{self.total_tasks} 已完成")
                return progress_data
                
        except Exception as e:
            logger.error(f"加载进度文件失败: {e}")
        
        return None
    
    def get_statistics(self) -> Dict:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        current_time = time.time()
        elapsed_time = current_time - self.start_time if self.start_time else 0
        
        stats = {
            'total_tasks': self.total_tasks,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'success_rate': self.completed_tasks / max(1, self.completed_tasks + self.failed_tasks) * 100,
            'progress_percentage': self.completed_tasks / self.total_tasks * 100,
            'elapsed_time': elapsed_time,
            'elapsed_time_formatted': self._format_time(elapsed_time),
            'estimated_total_time': elapsed_time / max(1, self.completed_tasks) * self.total_tasks if self.completed_tasks > 0 else 0,
            'average_task_time': sum(self.task_times) / len(self.task_times) if self.task_times else 0,
            'min_task_time': min(self.task_times) if self.task_times else 0,
            'max_task_time': max(self.task_times) if self.task_times else 0,
            'tasks_per_hour': self.completed_tasks / (elapsed_time / 3600) if elapsed_time > 0 else 0
        }
        
        return stats
    
    def _format_time(self, seconds: float) -> str:
        """
        格式化时间
        
        Args:
            seconds: 秒数
            
        Returns:
            格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds:.0f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"
    
    def print_summary(self):
        """打印总结信息"""
        stats = self.get_statistics()
        
        logger.info("=" * 50)
        logger.info("批量生成总结")
        logger.info("=" * 50)
        logger.info(f"总任务数: {stats['total_tasks']}")
        logger.info(f"完成任务数: {stats['completed_tasks']}")
        logger.info(f"失败任务数: {stats['failed_tasks']}")
        logger.info(f"成功率: {stats['success_rate']:.1f}%")
        logger.info(f"总耗时: {stats['elapsed_time_formatted']}")
        logger.info(f"平均每任务耗时: {stats['average_task_time']:.2f}秒")
        logger.info(f"处理速度: {stats['tasks_per_hour']:.1f}任务/小时")
        
        if self.failed_tasks > 0:
            logger.info("\n失败任务:")
            failed_tasks = [task for task in self.task_details if task['status'] == 'failed']
            for task in failed_tasks[-5:]:  # 显示最近5个失败任务
                logger.info(f"  任务 {task['task_id']}: {task.get('error', '未知错误')}")
        
        logger.info("=" * 50)
    
    def is_completed(self) -> bool:
        """
        检查是否所有任务都已完成
        
        Returns:
            是否完成
        """
        return (self.completed_tasks + self.failed_tasks) >= self.total_tasks
    
    def get_failed_task_ids(self) -> List[int]:
        """
        获取失败任务的ID列表
        
        Returns:
            失败任务ID列表
        """
        failed_ids = []
        for task in self.task_details:
            if task['status'] == 'failed':
                failed_ids.append(task['task_id'])
        return failed_ids
    
    def reset_failed_tasks(self):
        """重置失败任务计数"""
        # 移除失败任务的记录
        self.task_details = [task for task in self.task_details if task['status'] != 'failed']
        self.failed_tasks = 0
        logger.info("已重置失败任务计数")
        self._save_progress()
    
    def export_detailed_report(self, output_file: str):
        """
        导出详细报告
        
        Args:
            output_file: 输出文件路径
        """
        try:
            stats = self.get_statistics()
            
            report = {
                'summary': stats,
                'task_details': self.task_details,
                'generation_info': {
                    'start_time': datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else None,
                    'end_time': datetime.now().isoformat(),
                    'output_directory': self.output_dir
                }
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"详细报告已导出: {output_file}")
            
        except Exception as e:
            logger.error(f"导出详细报告失败: {e}")
