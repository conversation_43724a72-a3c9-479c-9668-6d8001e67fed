"""
视野计算器
"""
import math
from typing import Tuple, Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


class ViewCalculator:
    """视野计算器，用于计算相机参数以覆盖指定区域"""
    
    def __init__(self):
        """初始化视野计算器"""
        pass
    
    def calculate_orthographic_size(self, scene_bounds: Dict, margin_ratio: float = 0.1) -> float:
        """
        计算正交相机的尺寸以覆盖场景
        
        Args:
            scene_bounds: 场景边界字典，包含min_x, max_x, min_y, max_y等
            margin_ratio: 边界余量比例
            
        Returns:
            正交相机尺寸
        """
        # 计算场景尺寸
        scene_width = scene_bounds['max_x'] - scene_bounds['min_x']
        scene_height = scene_bounds['max_y'] - scene_bounds['min_y']
        
        # 取较大的尺寸作为基准
        max_dimension = max(scene_width, scene_height)
        
        # 添加边界余量
        orthographic_size = max_dimension * (1 + margin_ratio) / 2
        
        logger.debug(f"计算正交相机尺寸: {orthographic_size:.2f} (场景: {scene_width:.2f}x{scene_height:.2f})")
        return orthographic_size
    
    def calculate_camera_height(self, scene_bounds: Dict, min_height: float = 100.0) -> float:
        """
        计算相机高度以确保俯视效果
        
        Args:
            scene_bounds: 场景边界字典
            min_height: 最小高度
            
        Returns:
            相机高度
        """
        # 获取场景最高点
        max_z = scene_bounds.get('max_z', 0)
        
        # 计算场景的最大水平尺寸
        scene_width = scene_bounds['max_x'] - scene_bounds['min_x']
        scene_height = scene_bounds['max_y'] - scene_bounds['min_y']
        max_horizontal = max(scene_width, scene_height)
        
        # 相机高度应该足够高以获得良好的俯视效果
        # 使用场景最大水平尺寸的一定比例
        calculated_height = max_z + max_horizontal * 0.5
        
        # 确保不低于最小高度
        camera_height = max(calculated_height, min_height)
        
        logger.debug(f"计算相机高度: {camera_height:.2f} (场景最高点: {max_z:.2f})")
        return camera_height
    
    def calculate_camera_position(self, scene_bounds: Dict, height: Optional[float] = None) -> Tuple[float, float, float]:
        """
        计算相机位置以居中覆盖场景
        
        Args:
            scene_bounds: 场景边界字典
            height: 指定高度，如果为None则自动计算
            
        Returns:
            相机位置 (x, y, z)
        """
        # 计算场景中心
        center_x = (scene_bounds['min_x'] + scene_bounds['max_x']) / 2
        center_y = (scene_bounds['min_y'] + scene_bounds['max_y']) / 2
        
        # 计算或使用指定的高度
        if height is None:
            height = self.calculate_camera_height(scene_bounds)
            
        position = (center_x, center_y, height)
        logger.debug(f"计算相机位置: {position}")
        return position
    
    def calculate_perspective_fov(self, scene_bounds: Dict, camera_height: float) -> float:
        """
        计算透视相机的视野角度
        
        Args:
            scene_bounds: 场景边界字典
            camera_height: 相机高度
            
        Returns:
            视野角度（弧度）
        """
        # 计算场景的最大水平尺寸
        scene_width = scene_bounds['max_x'] - scene_bounds['min_x']
        scene_height = scene_bounds['max_y'] - scene_bounds['min_y']
        max_dimension = max(scene_width, scene_height)
        
        # 计算所需的视野角度
        # 使用三角函数：tan(fov/2) = (max_dimension/2) / camera_height
        half_fov = math.atan((max_dimension / 2) / camera_height)
        fov = 2 * half_fov
        
        # 添加一些余量
        fov *= 1.2
        
        # 限制在合理范围内
        fov = max(math.radians(10), min(fov, math.radians(120)))
        
        logger.debug(f"计算透视FOV: {math.degrees(fov):.2f}度")
        return fov
    
    def validate_coverage(self, camera_pos: Tuple[float, float, float], 
                         orthographic_size: float, scene_bounds: Dict) -> bool:
        """
        验证相机设置是否能完全覆盖场景
        
        Args:
            camera_pos: 相机位置
            orthographic_size: 正交相机尺寸
            scene_bounds: 场景边界
            
        Returns:
            是否能完全覆盖
        """
        camera_x, camera_y, camera_z = camera_pos
        
        # 计算相机视野边界
        view_min_x = camera_x - orthographic_size
        view_max_x = camera_x + orthographic_size
        view_min_y = camera_y - orthographic_size
        view_max_y = camera_y + orthographic_size
        
        # 检查是否完全覆盖场景
        covers_x = (view_min_x <= scene_bounds['min_x'] and 
                   view_max_x >= scene_bounds['max_x'])
        covers_y = (view_min_y <= scene_bounds['min_y'] and 
                   view_max_y >= scene_bounds['max_y'])
        
        coverage_valid = covers_x and covers_y
        
        if not coverage_valid:
            logger.warning(f"相机覆盖不足 - 视野: [{view_min_x:.1f}, {view_max_x:.1f}] x [{view_min_y:.1f}, {view_max_y:.1f}], "
                          f"场景: [{scene_bounds['min_x']:.1f}, {scene_bounds['max_x']:.1f}] x "
                          f"[{scene_bounds['min_y']:.1f}, {scene_bounds['max_y']:.1f}]")
        
        return coverage_valid
    
    def calculate_pixel_to_world_scale(self, orthographic_size: float, 
                                      image_size: Tuple[int, int]) -> Tuple[float, float]:
        """
        计算像素到世界坐标的缩放比例
        
        Args:
            orthographic_size: 正交相机尺寸
            image_size: 图像尺寸 (width, height)
            
        Returns:
            (x_scale, y_scale) 每像素对应的世界坐标单位
        """
        image_width, image_height = image_size
        
        # 正交相机的世界尺寸是 orthographic_size * 2
        world_width = orthographic_size * 2
        world_height = orthographic_size * 2
        
        # 计算每像素的世界坐标单位
        x_scale = world_width / image_width
        y_scale = world_height / image_height
        
        logger.debug(f"像素到世界坐标缩放: {x_scale:.4f}, {y_scale:.4f}")
        return (x_scale, y_scale)
    
    def world_to_pixel_coordinates(self, world_pos: Tuple[float, float], 
                                  camera_pos: Tuple[float, float, float],
                                  orthographic_size: float,
                                  image_size: Tuple[int, int]) -> Tuple[int, int]:
        """
        将世界坐标转换为像素坐标
        
        Args:
            world_pos: 世界坐标 (x, y)
            camera_pos: 相机位置 (x, y, z)
            orthographic_size: 正交相机尺寸
            image_size: 图像尺寸 (width, height)
            
        Returns:
            像素坐标 (pixel_x, pixel_y)
        """
        world_x, world_y = world_pos
        camera_x, camera_y, _ = camera_pos
        image_width, image_height = image_size
        
        # 计算相对于相机中心的偏移
        offset_x = world_x - camera_x
        offset_y = world_y - camera_y
        
        # 转换到相机坐标系 (-orthographic_size 到 +orthographic_size)
        norm_x = offset_x / orthographic_size  # -1 到 1
        norm_y = offset_y / orthographic_size  # -1 到 1
        
        # 转换到像素坐标 (注意Y轴翻转)
        pixel_x = int((norm_x + 1) * image_width / 2)
        pixel_y = int((1 - norm_y) * image_height / 2)  # Y轴翻转
        
        # 确保在图像范围内
        pixel_x = max(0, min(pixel_x, image_width - 1))
        pixel_y = max(0, min(pixel_y, image_height - 1))
        
        return (pixel_x, pixel_y)
    
    def calculate_object_pixel_bbox(self, object_bounds: Dict,
                                   camera_pos: Tuple[float, float, float],
                                   orthographic_size: float,
                                   image_size: Tuple[int, int]) -> Tuple[int, int, int, int]:
        """
        计算对象在图像中的像素边界框
        
        Args:
            object_bounds: 对象边界字典，包含min_x, max_x, min_y, max_y
            camera_pos: 相机位置
            orthographic_size: 正交相机尺寸
            image_size: 图像尺寸
            
        Returns:
            像素边界框 (min_x, min_y, max_x, max_y)
        """
        # 获取对象的四个角点
        corners = [
            (object_bounds['min_x'], object_bounds['min_y']),
            (object_bounds['max_x'], object_bounds['min_y']),
            (object_bounds['max_x'], object_bounds['max_y']),
            (object_bounds['min_x'], object_bounds['max_y'])
        ]
        
        # 转换所有角点到像素坐标
        pixel_corners = [
            self.world_to_pixel_coordinates(corner, camera_pos, orthographic_size, image_size)
            for corner in corners
        ]
        
        # 计算边界框
        pixel_xs = [corner[0] for corner in pixel_corners]
        pixel_ys = [corner[1] for corner in pixel_corners]
        
        min_pixel_x = min(pixel_xs)
        max_pixel_x = max(pixel_xs)
        min_pixel_y = min(pixel_ys)
        max_pixel_y = max(pixel_ys)
        
        return (min_pixel_x, min_pixel_y, max_pixel_x, max_pixel_y)
