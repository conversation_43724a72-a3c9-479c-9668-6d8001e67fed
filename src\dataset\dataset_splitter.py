"""
数据集分割器
"""
import json
import os
import random
import shutil
from typing import Dict, List, Tuple
import logging

try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config

logger = logging.getLogger(__name__)


class DatasetSplitter:
    """数据集分割器，用于将数据集分割为训练集、验证集和测试集"""
    
    def __init__(self):
        """初始化数据集分割器"""
        # 从配置获取分割比例
        self.train_ratio = config.get('dataset.train_ratio', 0.7)
        self.val_ratio = config.get('dataset.val_ratio', 0.2)
        self.test_ratio = config.get('dataset.test_ratio', 0.1)
        
        # 验证比例
        total_ratio = self.train_ratio + self.val_ratio + self.test_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            logger.warning(f"数据集分割比例总和不为1: {total_ratio}")
            # 归一化
            self.train_ratio /= total_ratio
            self.val_ratio /= total_ratio
            self.test_ratio /= total_ratio
    
    def split_coco_dataset(self, coco_file: str, output_dir: str, 
                          seed: int = 42) -> bool:
        """
        分割COCO数据集
        
        Args:
            coco_file: COCO数据集文件路径
            output_dir: 输出目录
            seed: 随机种子
            
        Returns:
            是否成功分割
        """
        try:
            # 设置随机种子
            random.seed(seed)
            
            # 加载COCO数据集
            with open(coco_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
            
            # 获取图像和标注
            images = coco_data['images']
            annotations = coco_data['annotations']
            
            # 按类别分组图像以确保均匀分布
            category_image_groups = self._group_images_by_category(images, annotations)
            
            # 分割每个类别的图像
            train_images, val_images, test_images = self._split_images_by_category(
                category_image_groups
            )
            
            # 创建分割后的数据集
            train_data = self._create_split_dataset(coco_data, train_images)
            val_data = self._create_split_dataset(coco_data, val_images)
            test_data = self._create_split_dataset(coco_data, test_images)
            
            # 保存分割后的数据集
            self._save_split_datasets(output_dir, train_data, val_data, test_data)
            
            # 打印统计信息
            self._print_split_statistics(train_data, val_data, test_data)
            
            logger.info(f"数据集分割完成，输出目录: {output_dir}")
            return True
            
        except Exception as e:
            logger.error(f"数据集分割失败: {e}")
            return False
    
    def _group_images_by_category(self, images: List[Dict], 
                                 annotations: List[Dict]) -> Dict[int, List[Dict]]:
        """
        按类别分组图像
        
        Args:
            images: 图像列表
            annotations: 标注列表
            
        Returns:
            按类别分组的图像字典
        """
        # 创建图像ID到图像的映射
        image_id_to_image = {img['id']: img for img in images}
        
        # 创建图像ID到类别的映射
        image_categories = {}
        for annotation in annotations:
            image_id = annotation['image_id']
            category_id = annotation['category_id']
            
            if image_id not in image_categories:
                image_categories[image_id] = set()
            image_categories[image_id].add(category_id)
        
        # 按类别分组图像
        category_groups = {}
        for image_id, categories in image_categories.items():
            image = image_id_to_image[image_id]
            
            # 如果图像包含多个类别，将其分配给第一个类别
            primary_category = min(categories)
            
            if primary_category not in category_groups:
                category_groups[primary_category] = []
            category_groups[primary_category].append(image)
        
        logger.info(f"按类别分组: {len(category_groups)} 个类别")
        for category_id, group_images in category_groups.items():
            logger.info(f"类别 {category_id}: {len(group_images)} 张图像")
        
        return category_groups
    
    def _split_images_by_category(self, category_groups: Dict[int, List[Dict]]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """
        按类别分割图像，确保每个类别在各个集合中的分布均匀
        
        Args:
            category_groups: 按类别分组的图像
            
        Returns:
            (训练集图像, 验证集图像, 测试集图像)
        """
        train_images = []
        val_images = []
        test_images = []
        
        for category_id, category_images in category_groups.items():
            # 随机打乱该类别的图像
            shuffled_images = category_images.copy()
            random.shuffle(shuffled_images)
            
            # 计算分割点
            total_count = len(shuffled_images)

            # 对于小数据集，确保每个集合至少有一张图像
            if total_count <= 3:
                # 小数据集特殊处理
                if total_count == 1:
                    train_count, val_count = 1, 0
                elif total_count == 2:
                    train_count, val_count = 1, 1
                else:  # total_count == 3
                    train_count, val_count = 2, 1
            else:
                # 正常分割
                train_count = max(1, int(total_count * self.train_ratio))
                val_count = max(1, int(total_count * self.val_ratio))
                # 确保不超过总数
                if train_count + val_count >= total_count:
                    val_count = max(0, total_count - train_count - 1)

            # 分割图像
            category_train = shuffled_images[:train_count]
            category_val = shuffled_images[train_count:train_count + val_count]
            category_test = shuffled_images[train_count + val_count:]
            
            # 添加到对应的集合
            train_images.extend(category_train)
            val_images.extend(category_val)
            test_images.extend(category_test)
            
            logger.debug(f"类别 {category_id} 分割: 训练 {len(category_train)}, "
                        f"验证 {len(category_val)}, 测试 {len(category_test)}")
        
        return train_images, val_images, test_images
    
    def _create_split_dataset(self, original_data: Dict, 
                            split_images: List[Dict]) -> Dict:
        """
        创建分割后的数据集
        
        Args:
            original_data: 原始COCO数据
            split_images: 分割后的图像列表
            
        Returns:
            分割后的数据集
        """
        # 获取分割图像的ID集合
        split_image_ids = set(img['id'] for img in split_images)
        
        # 过滤对应的标注
        split_annotations = [
            ann for ann in original_data['annotations']
            if ann['image_id'] in split_image_ids
        ]
        
        # 创建新的数据集
        split_data = {
            'info': original_data['info'].copy(),
            'licenses': original_data['licenses'].copy(),
            'categories': original_data['categories'].copy(),
            'images': split_images,
            'annotations': split_annotations
        }
        
        return split_data
    
    def _save_split_datasets(self, output_dir: str, 
                           train_data: Dict, val_data: Dict, test_data: Dict):
        """
        保存分割后的数据集
        
        Args:
            output_dir: 输出目录
            train_data: 训练集数据
            val_data: 验证集数据
            test_data: 测试集数据
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存各个数据集
        datasets = {
            'train': train_data,
            'val': val_data,
            'test': test_data
        }
        
        for split_name, split_data in datasets.items():
            output_file = os.path.join(output_dir, f'{split_name}_annotations.json')
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(split_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"保存 {split_name} 数据集: {output_file}")
    
    def _print_split_statistics(self, train_data: Dict, val_data: Dict, test_data: Dict):
        """
        打印分割统计信息
        
        Args:
            train_data: 训练集数据
            val_data: 验证集数据
            test_data: 测试集数据
        """
        datasets = {
            '训练集': train_data,
            '验证集': val_data,
            '测试集': test_data
        }
        
        logger.info("数据集分割统计:")
        
        for name, data in datasets.items():
            image_count = len(data['images'])
            annotation_count = len(data['annotations'])
            
            # 统计每个类别的标注数
            category_counts = {}
            for category in data['categories']:
                category_counts[category['id']] = 0
            
            for annotation in data['annotations']:
                category_id = annotation['category_id']
                if category_id in category_counts:
                    category_counts[category_id] += 1
            
            logger.info(f"{name}: {image_count} 张图像, {annotation_count} 个标注")
            
            for category in data['categories']:
                category_id = category['id']
                category_name = category['name']
                count = category_counts.get(category_id, 0)
                logger.info(f"  {category_name}: {count} 个实例")
    
    def copy_images_for_split(self, source_image_dir: str, 
                            output_dir: str, 
                            coco_files: Dict[str, str]) -> bool:
        """
        为分割后的数据集复制对应的图像文件
        
        Args:
            source_image_dir: 源图像目录
            output_dir: 输出目录
            coco_files: 分割后的COCO文件路径字典 {'train': path, 'val': path, 'test': path}
            
        Returns:
            是否成功复制
        """
        try:
            for split_name, coco_file in coco_files.items():
                # 创建目标目录
                target_image_dir = os.path.join(output_dir, split_name, 'images')
                os.makedirs(target_image_dir, exist_ok=True)
                
                # 加载COCO文件
                with open(coco_file, 'r', encoding='utf-8') as f:
                    coco_data = json.load(f)
                
                # 复制图像文件
                copied_count = 0
                for image_info in coco_data['images']:
                    filename = image_info['file_name']
                    source_path = os.path.join(source_image_dir, filename)
                    target_path = os.path.join(target_image_dir, filename)
                    
                    if os.path.exists(source_path):
                        shutil.copy2(source_path, target_path)
                        copied_count += 1
                    else:
                        logger.warning(f"源图像文件不存在: {source_path}")
                
                logger.info(f"{split_name} 集合复制了 {copied_count} 张图像")
            
            return True
            
        except Exception as e:
            logger.error(f"复制图像文件失败: {e}")
            return False
    
    def validate_split_balance(self, output_dir: str) -> Dict:
        """
        验证分割后数据集的平衡性
        
        Args:
            output_dir: 输出目录
            
        Returns:
            平衡性统计信息
        """
        try:
            # 加载分割后的数据集
            splits = ['train', 'val', 'test']
            split_data = {}
            
            for split in splits:
                coco_file = os.path.join(output_dir, f'{split}_annotations.json')
                if os.path.exists(coco_file):
                    with open(coco_file, 'r', encoding='utf-8') as f:
                        split_data[split] = json.load(f)
            
            # 计算平衡性统计
            balance_stats = {}
            
            for split, data in split_data.items():
                # 统计每个类别的实例数
                category_counts = {}
                for category in data['categories']:
                    category_counts[category['id']] = 0
                
                for annotation in data['annotations']:
                    category_id = annotation['category_id']
                    if category_id in category_counts:
                        category_counts[category_id] += 1
                
                balance_stats[split] = {
                    'total_images': len(data['images']),
                    'total_annotations': len(data['annotations']),
                    'category_counts': category_counts
                }
            
            # 计算类别分布的变异系数
            for category_id in split_data['train']['categories']:
                category_id = category_id['id']
                counts = [stats['category_counts'].get(category_id, 0) 
                         for stats in balance_stats.values()]
                
                if sum(counts) > 0:
                    mean_count = sum(counts) / len(counts)
                    variance = sum((c - mean_count)**2 for c in counts) / len(counts)
                    cv = (variance**0.5) / mean_count if mean_count > 0 else 0
                    
                    logger.info(f"类别 {category_id} 分布变异系数: {cv:.3f}")
            
            return balance_stats
            
        except Exception as e:
            logger.error(f"验证分割平衡性失败: {e}")
            return {}
