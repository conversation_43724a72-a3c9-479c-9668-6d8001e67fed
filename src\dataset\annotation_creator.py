"""
标注创建器
"""
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class AnnotationCreator:
    """标注创建器，用于从场景信息创建目标检测标注"""
    
    def __init__(self):
        """初始化标注创建器"""
        pass
    
    def create_annotations_from_scene(self, scene_info: Dict, 
                                    camera_params: Dict,
                                    image_size: Tuple[int, int]) -> List[Dict]:
        """
        从场景信息创建标注
        
        Args:
            scene_info: 场景信息字典
            camera_params: 相机参数字典
            image_size: 图像尺寸 (width, height)
            
        Returns:
            标注列表
        """
        annotations = []
        
        aircraft_list = scene_info.get('aircraft', [])
        
        for aircraft in aircraft_list:
            try:
                # 计算飞机在图像中的边界框
                bbox = self._calculate_image_bbox(aircraft, camera_params, image_size)
                
                if bbox is None:
                    logger.warning(f"无法计算飞机 {aircraft.get('name', 'unknown')} 的边界框")
                    continue
                
                # 检查边界框是否在图像范围内
                if not self._is_bbox_valid(bbox, image_size):
                    logger.debug(f"飞机 {aircraft.get('name', 'unknown')} 的边界框超出图像范围")
                    continue
                
                # 创建标注
                annotation = {
                    'aircraft_name': aircraft.get('name', 'unknown'),
                    'aircraft_type': aircraft.get('aircraft_type', 'unknown'),
                    'category_id': aircraft.get('category_id', 0),
                    'instance_id': aircraft.get('instance_id', 0),
                    'bbox': bbox,  # [x, y, width, height]
                    'area': bbox[2] * bbox[3],
                    'iscrowd': 0,
                    'world_location': aircraft.get('location', [0, 0, 0]),
                    'world_rotation': aircraft.get('rotation', [0, 0, 0]),
                    'world_bbox': aircraft.get('bbox_world', {}),
                    'dimensions': aircraft.get('dimensions', {})
                }
                
                annotations.append(annotation)
                
            except Exception as e:
                logger.error(f"创建飞机标注失败 {aircraft.get('name', 'unknown')}: {e}")
                continue
        
        logger.info(f"创建了 {len(annotations)} 个标注")
        return annotations
    
    def _calculate_image_bbox(self, aircraft: Dict, 
                            camera_params: Dict,
                            image_size: Tuple[int, int]) -> Optional[Tuple[int, int, int, int]]:
        """
        计算飞机在图像中的边界框
        
        Args:
            aircraft: 飞机信息
            camera_params: 相机参数
            image_size: 图像尺寸
            
        Returns:
            边界框 (x, y, width, height) 或 None
        """
        try:
            # 获取世界坐标边界框
            world_bbox = aircraft.get('bbox_world', {})
            if not world_bbox:
                logger.warning(f"飞机 {aircraft.get('name', 'unknown')} 缺少世界坐标边界框")
                return None

            # 检查边界框是否有效
            required_keys = ['min_x', 'max_x', 'min_y', 'max_y']
            if not all(key in world_bbox for key in required_keys):
                logger.warning(f"飞机 {aircraft.get('name', 'unknown')} 边界框缺少必要字段")
                return None

            # 获取相机参数
            camera_pos = camera_params.get('camera_position', (0, 0, 0))
            orthographic_size = camera_params.get('orthographic_size', 1000)

            logger.debug(f"计算边界框: 飞机={aircraft.get('name', 'unknown')}, "
                        f"世界边界框={world_bbox}, 相机位置={camera_pos}, 正交大小={orthographic_size}")
            
            # 计算世界坐标到像素坐标的转换
            image_width, image_height = image_size
            
            # 世界坐标范围
            world_min_x = camera_pos[0] - orthographic_size
            world_max_x = camera_pos[0] + orthographic_size
            world_min_y = camera_pos[1] - orthographic_size
            world_max_y = camera_pos[1] + orthographic_size
            
            world_width = world_max_x - world_min_x
            world_height = world_max_y - world_min_y
            
            # 转换飞机边界框的四个角点
            corners_world = [
                (world_bbox['min_x'], world_bbox['min_y']),
                (world_bbox['max_x'], world_bbox['min_y']),
                (world_bbox['max_x'], world_bbox['max_y']),
                (world_bbox['min_x'], world_bbox['max_y'])
            ]
            
            corners_pixel = []
            for wx, wy in corners_world:
                # 转换到像素坐标
                px = int((wx - world_min_x) / world_width * image_width)
                py = int((wy - world_min_y) / world_height * image_height)
                
                # 注意：图像坐标系Y轴向下，需要翻转
                py = image_height - py
                
                corners_pixel.append((px, py))
            
            # 计算边界框
            pixel_xs = [corner[0] for corner in corners_pixel]
            pixel_ys = [corner[1] for corner in corners_pixel]

            min_x = max(0, min(pixel_xs))
            max_x = min(image_width - 1, max(pixel_xs))
            min_y = max(0, min(pixel_ys))
            max_y = min(image_height - 1, max(pixel_ys))

            # 计算宽度和高度
            width = max_x - min_x + 1  # 加1确保至少有1像素宽度
            height = max_y - min_y + 1  # 加1确保至少有1像素高度

            # 确保边界框有效
            if width <= 0 or height <= 0:
                logger.debug(f"边界框尺寸无效: width={width}, height={height}")
                return None

            # 确保边界框在图像范围内
            if min_x >= image_width or min_y >= image_height:
                logger.debug(f"边界框超出图像范围: min_x={min_x}, min_y={min_y}")
                return None

            logger.debug(f"计算得到边界框: ({min_x}, {min_y}, {width}, {height})")
            return (min_x, min_y, width, height)
            
        except Exception as e:
            logger.error(f"计算图像边界框失败: {e}")
            return None
    
    def _is_bbox_valid(self, bbox: Tuple[int, int, int, int], 
                      image_size: Tuple[int, int]) -> bool:
        """
        检查边界框是否有效
        
        Args:
            bbox: 边界框 (x, y, width, height)
            image_size: 图像尺寸 (width, height)
            
        Returns:
            是否有效
        """
        x, y, width, height = bbox
        image_width, image_height = image_size
        
        # 检查边界框是否在图像范围内（允许部分超出）
        if x >= image_width or y >= image_height or x + width <= 0 or y + height <= 0:
            return False

        # 检查边界框是否有足够的面积
        if width <= 0 or height <= 0:
            return False
        
        # 检查边界框尺寸是否合理
        min_size = 5  # 最小像素尺寸
        if width < min_size or height < min_size:
            return False
        
        # 检查边界框面积是否合理
        area = width * height
        max_area = image_width * image_height * 0.8  # 最大不超过图像面积的80%
        if area > max_area:
            return False
        
        return True
    
    def filter_annotations_by_visibility(self, annotations: List[Dict],
                                       min_area: int = 25,
                                       min_visibility: float = 0.5) -> List[Dict]:
        """
        根据可见性过滤标注
        
        Args:
            annotations: 标注列表
            min_area: 最小面积
            min_visibility: 最小可见性比例
            
        Returns:
            过滤后的标注列表
        """
        filtered_annotations = []
        
        for annotation in annotations:
            try:
                bbox = annotation['bbox']
                area = bbox[2] * bbox[3]

                # 检查面积 - 降低最小面积要求
                if area < max(min_area, 10):  # 至少10像素
                    logger.debug(f"标注面积太小: {area}")
                    continue

                # 这里可以添加更复杂的可见性检查
                # 例如检查目标是否被其他目标遮挡

                filtered_annotations.append(annotation)

            except Exception as e:
                logger.warning(f"过滤标注时出错: {e}")
                continue
        
        logger.info(f"可见性过滤: {len(annotations)} -> {len(filtered_annotations)}")
        return filtered_annotations
    
    def merge_overlapping_annotations(self, annotations: List[Dict],
                                    iou_threshold: float = 0.5) -> List[Dict]:
        """
        合并重叠的标注
        
        Args:
            annotations: 标注列表
            iou_threshold: IoU阈值
            
        Returns:
            合并后的标注列表
        """
        if len(annotations) <= 1:
            return annotations
        
        merged_annotations = []
        used_indices = set()
        
        for i, ann1 in enumerate(annotations):
            if i in used_indices:
                continue
            
            # 查找与当前标注重叠的其他标注
            overlapping_indices = [i]
            
            for j, ann2 in enumerate(annotations[i+1:], i+1):
                if j in used_indices:
                    continue
                
                iou = self._calculate_bbox_iou(ann1['bbox'], ann2['bbox'])
                if iou > iou_threshold:
                    overlapping_indices.append(j)
            
            # 如果有重叠，合并标注
            if len(overlapping_indices) > 1:
                merged_ann = self._merge_annotations([annotations[idx] for idx in overlapping_indices])
                merged_annotations.append(merged_ann)
                used_indices.update(overlapping_indices)
            else:
                merged_annotations.append(ann1)
                used_indices.add(i)
        
        logger.info(f"重叠合并: {len(annotations)} -> {len(merged_annotations)}")
        return merged_annotations
    
    def _calculate_bbox_iou(self, bbox1: Tuple[int, int, int, int],
                           bbox2: Tuple[int, int, int, int]) -> float:
        """
        计算两个边界框的IoU
        
        Args:
            bbox1: 边界框1 (x, y, width, height)
            bbox2: 边界框2 (x, y, width, height)
            
        Returns:
            IoU值
        """
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 计算交集
        x_left = max(x1, x2)
        y_top = max(y1, y2)
        x_right = min(x1 + w1, x2 + w2)
        y_bottom = min(y1 + h1, y2 + h2)
        
        if x_right <= x_left or y_bottom <= y_top:
            return 0.0
        
        intersection = (x_right - x_left) * (y_bottom - y_top)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def _merge_annotations(self, annotations: List[Dict]) -> Dict:
        """
        合并多个标注
        
        Args:
            annotations: 要合并的标注列表
            
        Returns:
            合并后的标注
        """
        if len(annotations) == 1:
            return annotations[0]
        
        # 计算合并后的边界框
        all_bboxes = [ann['bbox'] for ann in annotations]
        
        min_x = min(bbox[0] for bbox in all_bboxes)
        min_y = min(bbox[1] for bbox in all_bboxes)
        max_x = max(bbox[0] + bbox[2] for bbox in all_bboxes)
        max_y = max(bbox[1] + bbox[3] for bbox in all_bboxes)
        
        merged_bbox = (min_x, min_y, max_x - min_x, max_y - min_y)
        
        # 使用第一个标注作为基础
        merged_annotation = annotations[0].copy()
        merged_annotation['bbox'] = merged_bbox
        merged_annotation['area'] = merged_bbox[2] * merged_bbox[3]
        
        # 合并名称（如果有多个不同的飞机）
        aircraft_names = [ann['aircraft_name'] for ann in annotations]
        if len(set(aircraft_names)) > 1:
            merged_annotation['aircraft_name'] = '_'.join(aircraft_names)
        
        return merged_annotation
    
    def validate_annotations(self, annotations: List[Dict],
                           image_size: Tuple[int, int]) -> List[Dict]:
        """
        验证标注的有效性
        
        Args:
            annotations: 标注列表
            image_size: 图像尺寸
            
        Returns:
            有效的标注列表
        """
        valid_annotations = []
        
        for annotation in annotations:
            try:
                bbox = annotation['bbox']
                
                # 检查边界框格式
                if len(bbox) != 4:
                    logger.warning(f"边界框格式错误: {bbox}")
                    continue
                
                # 检查边界框有效性
                if not self._is_bbox_valid(bbox, image_size):
                    logger.warning(f"边界框无效: {bbox}")
                    continue
                
                # 检查必需字段
                required_fields = ['aircraft_type', 'category_id']
                if not all(field in annotation for field in required_fields):
                    logger.warning(f"标注缺少必需字段: {annotation}")
                    continue
                
                valid_annotations.append(annotation)
                
            except Exception as e:
                logger.error(f"验证标注失败: {e}")
                continue
        
        logger.info(f"标注验证: {len(annotations)} -> {len(valid_annotations)}")
        return valid_annotations
