# ===================================
# SAR Image Simulation Dataset Generator
# ===================================

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
pip-log.txt
pip-delete-this-directory.txt

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
conda-meta/
.conda/

# IDE and Editors
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace
*.code-workspace

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===================================
# SAR Simulation Specific
# ===================================

# Generated datasets and outputs
output/
output_*/
my_dataset/
datasets/
generated_data/

# Logs and debug files
logs/
*.log
blender_output.log
debug_*.log
error_*.log

# Temporary and test files
test_*.py
debug_*.py
temp_*.py
analyze_*.py
compare_*.py
check_*.py
*.tmp
*.temp
*.bak

# Generated images (except assets)
*.png
*.jpg
*.jpeg
*.tiff
*.tif
*.bmp
*.gif

# Except assets and documentation images
!assets/**/*.png
!assets/**/*.jpg
!assets/**/*.jpeg
!assets/**/*.tiff
!assets/**/*.tif
!docs/**/*.png
!docs/**/*.jpg
!docs/**/*.jpeg
!README_images/
!examples/**/*.png

# Blender specific
*.blend1
*.blend2
*.blend~
blender_temp/
.blender/

# 3D model cache
*.cache
*.blend_cache/

# SAR processing cache
sar_cache/
processing_cache/
temp_scenes/

# Configuration backups
config_backup.yaml
config.yaml.bak
config_*.yaml

# Large model files (should be tracked with Git LFS)
*.fbx
*.obj
*.dae
*.3ds
*.max
*.ma
*.mb

# Except essential model files in assets
!assets/jets/*.fbx
!assets/models/*.obj

# Dataset splits and annotations (large files)
annotations_large/
splits_large/
coco_large.json

# Performance and profiling
*.prof
*.profile
profiling_results/

# Documentation build
docs/_build/
docs/build/
site/

# Jupyter notebooks checkpoints
.ipynb_checkpoints/
*.ipynb

# pytest
.pytest_cache/
.coverage
htmlcov/
.tox/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# ===================================
# Project specific exclusions
# ===================================

# User-specific configuration
user_config.yaml
local_config.yaml
.env.local

# Development and testing
dev_output/
test_output/
sandbox/
experiments/

# Backup directories
backup/
backups/
old/

# Large datasets (should use Git LFS)
large_datasets/
*.zip
*.tar.gz
*.rar
*.7z

# Except small example datasets
!examples/sample_data.zip

# Debug directories
debug/