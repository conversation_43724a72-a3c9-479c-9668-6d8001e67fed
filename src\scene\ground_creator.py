"""
地面场景创建器
"""
from typing import Tuple, Optional
import logging

try:
    import bpy
    import bmesh
    from mathutils import Vector
    BLENDER_AVAILABLE = True
except ImportError:
    BLENDER_AVAILABLE = False
    class MockObject:
        def __init__(self, name):
            self.name = name
            self.type = 'MESH'
            self.location = (0, 0, 0)
    bpy = None

try:
    from ..utils import config
    from ..models import MaterialManager
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config
    from models import MaterialManager

logger = logging.getLogger(__name__)


class GroundCreator:
    """地面场景创建器"""
    
    def __init__(self):
        """初始化地面创建器"""
        self.ground_size = config.get('scene.ground_size', [1000, 1000])
        self.material_manager = MaterialManager()
        
    def create_ground_plane(self, name: str = "Ground"):
        """
        创建地面平面

        Args:
            name: 地面对象名称

        Returns:
            创建的地面对象
        """
        try:
            if not BLENDER_AVAILABLE:
                return MockObject(name)

            # 删除现有的地面对象
            if name in bpy.data.objects:
                bpy.data.objects.remove(bpy.data.objects[name], do_unlink=True)
                
            # 创建网格
            mesh = bpy.data.meshes.new(name)
            obj = bpy.data.objects.new(name, mesh)
            
            # 添加到场景
            bpy.context.collection.objects.link(obj)
            
            # 创建地面几何体
            bm = bmesh.new()
            
            # 创建平面
            bmesh.ops.create_grid(
                bm,
                x_segments=100,  # 增加细分以提高SAR仿真精度
                y_segments=100,
                size=max(self.ground_size) / 2
            )
            
            # 缩放到指定尺寸
            scale_x = self.ground_size[0] / max(self.ground_size)
            scale_y = self.ground_size[1] / max(self.ground_size)
            
            bmesh.ops.scale(
                bm,
                vec=(scale_x, scale_y, 1.0),
                verts=bm.verts
            )
            
            # 应用到网格
            bm.to_mesh(mesh)
            bm.free()
            
            # 设置位置（Z=0，即地面高度）
            obj.location = (0, 0, 0)

            # 应用地面材质（如果失败则继续）
            try:
                self.material_manager.setup_ground_material(obj)
                logger.info(f"地面材质设置成功")
            except Exception as material_error:
                logger.warning(f"地面材质设置失败，但继续: {material_error}")

            logger.info(f"创建地面平面: {name}, 尺寸: {self.ground_size}")
            return obj
            
        except Exception as e:
            logger.error(f"创建地面平面失败: {e}")
            return None
    
    def create_terrain(self, name: str = "Terrain",
                      height_variation: float = 5.0):
        """
        创建带有高度变化的地形
        
        Args:
            name: 地形对象名称
            height_variation: 高度变化范围（米）
            
        Returns:
            创建的地形对象
        """
        try:
            # 删除现有的地形对象
            if name in bpy.data.objects:
                bpy.data.objects.remove(bpy.data.objects[name], do_unlink=True)
                
            # 创建网格
            mesh = bpy.data.meshes.new(name)
            obj = bpy.data.objects.new(name, mesh)
            
            # 添加到场景
            bpy.context.collection.objects.link(obj)
            
            # 创建地形几何体
            bm = bmesh.new()
            
            # 创建细分平面
            bmesh.ops.create_grid(
                bm,
                x_segments=200,  # 高细分度用于地形
                y_segments=200,
                size=max(self.ground_size) / 2
            )
            
            # 缩放到指定尺寸
            scale_x = self.ground_size[0] / max(self.ground_size)
            scale_y = self.ground_size[1] / max(self.ground_size)
            
            bmesh.ops.scale(
                bm,
                vec=(scale_x, scale_y, 1.0),
                verts=bm.verts
            )
            
            # 添加随机高度变化
            import random
            for vert in bm.verts:
                # 使用Perlin噪声模拟自然地形
                height_offset = random.uniform(-height_variation/2, height_variation/2)
                vert.co.z += height_offset
            
            # 平滑地形
            bmesh.ops.smooth_vert(
                bm,
                verts=bm.verts,
                factor=0.5,
                repeat=3
            )
            
            # 应用到网格
            bm.to_mesh(mesh)
            bm.free()
            
            # 设置位置
            obj.location = (0, 0, 0)
            
            # 应用地面材质
            self.material_manager.setup_ground_material(obj)
            
            logger.info(f"创建地形: {name}, 高度变化: ±{height_variation/2:.1f}m")
            return obj
            
        except Exception as e:
            logger.error(f"创建地形失败: {e}")
            return None
    
    def add_ground_features(self, ground_obj) -> bool:
        """
        为地面添加特征（如纹理、细节等）
        
        Args:
            ground_obj: 地面对象
            
        Returns:
            是否成功
        """
        if not ground_obj or ground_obj.type != 'MESH':
            return False
            
        try:
            # 进入编辑模式
            bpy.context.view_layer.objects.active = ground_obj
            bpy.ops.object.mode_set(mode='EDIT')
            
            # 添加细分修改器以增加细节
            bpy.ops.object.mode_set(mode='OBJECT')
            
            # 添加细分表面修改器
            subdiv_modifier = ground_obj.modifiers.new(name="Subdivision", type='SUBSURF')
            subdiv_modifier.levels = 1
            subdiv_modifier.render_levels = 2
            
            # 添加置换修改器用于微地形
            displace_modifier = ground_obj.modifiers.new(name="Displace", type='DISPLACE')
            displace_modifier.strength = 0.1
            displace_modifier.mid_level = 0.5
            
            # 创建噪声纹理用于置换
            noise_texture = bpy.data.textures.new(name="GroundNoise", type='NOISE')
            noise_texture.noise_scale = 0.1
            displace_modifier.texture = noise_texture
            
            logger.info(f"为地面添加特征: {ground_obj.name}")
            return True
            
        except Exception as e:
            logger.error(f"添加地面特征失败: {e}")
            return False
    
    def get_ground_bounds(self) -> Tuple[float, float, float, float]:
        """
        获取地面边界
        
        Returns:
            (min_x, max_x, min_y, max_y)
        """
        half_x = self.ground_size[0] / 2
        half_y = self.ground_size[1] / 2
        
        return (-half_x, half_x, -half_y, half_y)
    
    def is_point_on_ground(self, x: float, y: float, margin: float = 10.0) -> bool:
        """
        检查点是否在地面范围内
        
        Args:
            x, y: 点坐标
            margin: 边界余量
            
        Returns:
            是否在地面范围内
        """
        min_x, max_x, min_y, max_y = self.get_ground_bounds()
        
        return (min_x + margin <= x <= max_x - margin and 
                min_y + margin <= y <= max_y - margin)
    
    def get_ground_height_at_point(self, x: float, y: float,
                                  ground_obj = None) -> float:
        """
        获取指定点的地面高度
        
        Args:
            x, y: 点坐标
            ground_obj: 地面对象，如果为None则返回0
            
        Returns:
            地面高度
        """
        if not ground_obj or ground_obj.type != 'MESH':
            return 0.0
            
        # 简化实现：返回地面平均高度
        # 实际实现可以使用射线投射来获取精确高度
        try:
            # 获取地面对象的边界框
            bbox = [ground_obj.matrix_world @ Vector(corner) for corner in ground_obj.bound_box]
            z_coords = [v.z for v in bbox]
            return sum(z_coords) / len(z_coords)
            
        except Exception:
            return 0.0
