# SAR图像仿真数据集生成器使用指南

## 快速开始

### 1. 环境准备

确保您的系统满足以下要求：
- Python 3.8+
- Blender 4.5+（安装在指定路径）
- 足够的存储空间（推荐100GB+用于大规模数据集）

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置设置

编辑 `config.yaml` 文件：

```yaml
# 必须配置的关键参数
blender:
  executable_path: "D:/Softwares/Blender Foundation/Blender 4.5/blender.exe"  # 您的Blender路径

dataset:
  output_dir: "./output"
  total_images: 1000      # 要生成的图像数量
  image_size: [512, 512]  # 图像尺寸

aircraft:
  models_dir: "./assets/jets"  # 飞机模型目录
```

### 4. 运行基本示例

```bash
# 运行基本演示（生成10张图像）
python examples/basic_usage.py --demo basic

# 测试所有组件
python examples/basic_usage.py --demo components

# 演示SAR物理原理
python examples/basic_usage.py --demo physics

# 运行所有演示
python examples/basic_usage.py --demo all
```

## 详细使用说明

### 生成完整数据集

```bash
# 生成完整数据集
python main.py

# 使用自定义配置
python main.py --config custom_config.yaml

# 指定输出目录和图像数量
python main.py --output ./my_dataset --num-images 5000

# 无头模式运行（推荐用于服务器）
python main.py --headless

# 详细输出
python main.py --verbose
```

### 恢复中断的生成

如果生成过程被中断，可以从上次停止的地方继续：

```bash
python main.py --resume
```

### 配置参数详解

#### 数据集配置
```yaml
dataset:
  output_dir: "./output"        # 输出目录
  image_size: [512, 512]        # 图像尺寸
  total_images: 10000           # 总图像数
  train_ratio: 0.7              # 训练集比例
  val_ratio: 0.2                # 验证集比例
  test_ratio: 0.1               # 测试集比例
```

#### 飞机模型配置
```yaml
aircraft:
  models_dir: "./assets/jets"
  standard_length: 20.0         # 标准化长度（米）
  models:
    - name: "F16"
      file: "F16.FBX"
      category_id: 1
      length: 15.06             # 真实长度（米）
```

#### 场景配置
```yaml
scene:
  ground_size: [1000, 1000]     # 地面尺寸（米）
  aircraft_count:
    min: 3                      # 最少飞机数
    max: 15                     # 最多飞机数
  placement:
    height_range: [0, 50]       # 高度范围（米）
    pitch_range: [-15, 15]      # 俯仰角范围（度）
    yaw_range: [0, 360]         # 偏航角范围（度）
    min_distance: 30            # 最小间距（米）
```

#### SAR成像参数
```yaml
sar:
  frequency: 9.6e9              # X波段频率（Hz）
  bandwidth: 600e6              # 带宽（Hz）
  range_resolution: 0.25        # 距离分辨率（米）
  azimuth_resolution: 0.25      # 方位分辨率（米）
  noise:
    thermal_noise_power: -80    # 热噪声功率（dBm）
    clutter_ratio: 0.1          # 杂波比
    speckle_variance: 0.5       # 斑点噪声方差
```

## 输出结构

生成的数据集具有以下结构：

```
output/
├── images/                     # SAR图像文件
│   ├── sar_image_000001.png
│   ├── sar_image_000002.png
│   └── ...
├── annotations/                # 标注文件
│   └── instances.json          # COCO格式标注
├── scenes/                     # 场景信息（可选）
│   ├── scene_000001.json
│   └── ...
├── splits/                     # 数据集分割
│   ├── train_annotations.json
│   ├── val_annotations.json
│   ├── test_annotations.json
│   ├── train/
│   ├── val/
│   └── test/
├── logs/                       # 日志文件
├── progress.json               # 进度信息
├── dataset_statistics.json    # 数据集统计
└── detailed_report.json       # 详细报告
```

## 高级用法

### 自定义SAR成像参数

```python
# 创建自定义噪声参数
noise_params = {
    'thermal_noise_power': -70,    # 更强的热噪声
    'clutter_ratio': 0.2,          # 更多杂波
    'speckle_variance': 0.8,       # 更强的斑点噪声
    'add_quantization': True,      # 添加量化噪声
    'quantization_bits': 8
}

# 在代码中使用
from sar import SARImaging
sar_imaging = SARImaging()
sar_image, info = sar_imaging.simulate_sar_image_from_scene(
    scene_info, camera_params, noise_params
)
```

### 批量处理优化

```python
# 设置并行处理
from batch import BatchGenerator

batch_generator = BatchGenerator()

# 自定义批处理参数
batch_generator.batch_size = 50        # 批处理大小
batch_generator.max_workers = 8        # 最大工作进程数

# 生成数据集
batch_generator.generate_dataset()
```

### 数据集质量验证

```python
# 验证生成的数据集
from dataset import COCOGenerator, DatasetSplitter

# 加载并验证COCO数据集
coco_gen = COCOGenerator()
coco_gen.load_dataset('output/annotations/instances.json')

if coco_gen.validate_dataset():
    print("数据集验证通过")
    
    # 获取统计信息
    stats = coco_gen.get_dataset_statistics()
    print(f"总图像数: {stats['total_images']}")
    print(f"总标注数: {stats['total_annotations']}")
    print(f"类别分布: {stats['category_counts']}")
```

## 性能优化

### 硬件优化
- 使用SSD存储以提高I/O性能
- 配置GPU加速Blender渲染
- 增加系统内存以支持更大的批处理

### 软件优化
```bash
# 设置环境变量优化性能
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
export BLENDER_HEADLESS=1

# 运行优化版本
python main.py --headless --num-images 10000
```

### 配置优化
```yaml
# 在config.yaml中添加性能配置
performance:
  batch_size: 100
  max_workers: 8
  memory_limit_mb: 8192
  gpu_acceleration: true
```

## 故障排除

### 常见问题

1. **Blender路径错误**
   ```
   错误: Blender可执行文件不存在
   解决: 检查config.yaml中的blender.executable_path设置
   ```

2. **内存不足**
   ```
   错误: MemoryError
   解决: 减少batch_size或增加系统内存
   ```

3. **模型加载失败**
   ```
   错误: 无法加载飞机模型
   解决: 检查assets/jets目录中的FBX文件是否存在
   ```

4. **权限问题**
   ```
   错误: Permission denied
   解决: 确保输出目录有写入权限
   ```

### 调试模式

```bash
# 启用详细日志
python main.py --verbose

# 运行测试
python run_tests.py

# 检查配置
python -c "from src.utils import config; print(config.validate_config())"
```

## 扩展开发

### 添加新的飞机模型

1. 将FBX文件放入 `assets/jets/` 目录
2. 在 `config.yaml` 中添加模型配置：

```yaml
aircraft:
  models:
    - name: "NewAircraft"
      file: "NewAircraft.fbx"
      category_id: 8
      length: 25.0
```

### 自定义SAR算法

```python
# 继承并扩展SAR成像类
from sar import SARImaging

class CustomSARImaging(SARImaging):
    def custom_processing(self, raw_data):
        # 实现自定义处理算法
        pass
```

### 添加新的噪声类型

```python
# 扩展噪声生成器
from sar import NoiseGenerator

class ExtendedNoiseGenerator(NoiseGenerator):
    def add_custom_noise(self, signal, params):
        # 实现自定义噪声
        pass
```

## 技术支持

如果遇到问题，请：

1. 查看日志文件 `logs/sar_sim_*.log`
2. 运行测试套件 `python run_tests.py`
3. 检查系统资源使用情况
4. 参考性能优化指南 `PERFORMANCE_OPTIMIZATION.md`

更多详细信息请参考项目文档和源代码注释。
