"""
场景生成器
"""
import random
from typing import List, Dict, Optional, Tuple
import logging

try:
    import bpy
    BLENDER_AVAILABLE = True
except ImportError:
    BLENDER_AVAILABLE = False
    bpy = None

try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config
from .ground_creator import GroundCreator
from .aircraft_placer import AircraftPlacer

logger = logging.getLogger(__name__)


class SceneGenerator:
    """场景生成器，负责创建完整的SAR仿真场景"""
    
    def __init__(self):
        """初始化场景生成器"""
        self.ground_creator = GroundCreator()
        self.aircraft_placer = AircraftPlacer()
        
        # 设置地面边界给飞机放置器
        ground_bounds = self.ground_creator.get_ground_bounds()
        self.aircraft_placer.set_ground_bounds(ground_bounds)
        
        self.current_scene_info = None
        
    def clear_scene(self):
        """清除当前场景"""
        if not BLENDER_AVAILABLE:
            logger.info("场景已清除（模拟模式）")
            return

        # 清除所有网格对象
        mesh_objects = [obj for obj in bpy.context.scene.objects if obj.type == 'MESH']
        for obj in mesh_objects:
            bpy.data.objects.remove(obj, do_unlink=True)

        # 清除材质
        for material in bpy.data.materials:
            bpy.data.materials.remove(material, do_unlink=True)

        # 清除网格数据
        for mesh in bpy.data.meshes:
            bpy.data.meshes.remove(mesh, do_unlink=True)

        # 清除飞机加载器缓存，防止引用已删除的对象
        self.aircraft_placer.aircraft_loader.clear_cache()

        logger.info("场景已清除")
    
    def generate_scene(self, scene_id: int, seed: Optional[int] = None) -> Dict:
        """
        生成完整的SAR仿真场景
        
        Args:
            scene_id: 场景ID
            seed: 随机种子
            
        Returns:
            场景信息字典
        """
        if seed is not None:
            random.seed(seed)
            
        logger.info(f"开始生成场景 {scene_id}")
        
        try:
            if not BLENDER_AVAILABLE:
                # 在测试环境中返回模拟场景信息
                mock_info = self._generate_mock_scene_info(scene_id)
                logger.info(f"返回模拟场景信息: {len(mock_info)} 个键")
                return mock_info

            # 1. 清除现有场景
            self.clear_scene()

            # 2. 创建地面
            ground_obj = self.ground_creator.create_ground_plane(f"Ground_{scene_id}")
            if not ground_obj:
                raise Exception("创建地面失败")

            # 确保飞机放置器有正确的地面边界
            ground_bounds = self.ground_creator.get_ground_bounds()
            self.aircraft_placer.set_ground_bounds(ground_bounds)
            logger.debug(f"设置地面边界: {ground_bounds}")

            # 3. 生成飞机放置计划
            placement_plan = self.aircraft_placer.generate_aircraft_placement_plan(seed)
            aircraft_objects = []

            if placement_plan:
                # 4. 优化放置计划
                optimized_plan = self.aircraft_placer.optimize_placement(placement_plan)

                # 5. 放置飞机
                aircraft_objects = self.aircraft_placer.place_aircraft_from_plan(optimized_plan)
                if not aircraft_objects:
                    logger.warning("放置飞机失败，生成空场景")
                    aircraft_objects = []
            else:
                logger.warning("生成飞机放置计划失败，生成空场景")

            # 6. 验证场景
            if not self.aircraft_placer.validate_placement(aircraft_objects):
                logger.warning(f"场景 {scene_id} 验证失败，但继续处理")

            # 7. 收集场景信息
            try:
                scene_info = self._collect_scene_info(scene_id, ground_obj, aircraft_objects)
                if not scene_info or len(scene_info) == 0:
                    # 如果收集失败，创建基本场景信息
                    scene_info = {
                        'scene_id': scene_id,
                        'timestamp': 0,
                        'ground': {
                            'object_name': ground_obj.name if ground_obj else f'Ground_{scene_id}',
                            'bounds': (-500, 500, -500, 500),
                            'size': [1000, 1000]
                        },
                        'aircraft': [],
                        'aircraft_count': len(aircraft_objects) if aircraft_objects else 0,
                        'aircraft_types': [],
                        'scene_bounds': {
                            'min_x': -500, 'max_x': 500,
                            'min_y': -500, 'max_y': 500,
                            'min_z': 0, 'max_z': 50
                        }
                    }
                    logger.warning("场景信息收集失败，使用基本信息")

                self.current_scene_info = scene_info

                logger.info(f"场景 {scene_id} 生成完成，包含 {len(aircraft_objects)} 架飞机")
                return scene_info

            except Exception as collect_error:
                logger.error(f"收集场景信息时发生错误: {collect_error}")
                # 返回基本场景信息
                basic_scene_info = {
                    'scene_id': scene_id,
                    'timestamp': 0,
                    'ground': {
                        'object_name': f'Ground_{scene_id}',
                        'bounds': (-500, 500, -500, 500),
                        'size': [1000, 1000]
                    },
                    'aircraft': [],
                    'aircraft_count': len(aircraft_objects) if aircraft_objects else 0,
                    'aircraft_types': [],
                    'scene_bounds': {
                        'min_x': -500, 'max_x': 500,
                        'min_y': -500, 'max_y': 500,
                        'min_z': 0, 'max_z': 50
                    }
                }
                self.current_scene_info = basic_scene_info
                logger.info(f"场景 {scene_id} 生成完成（使用基本信息），包含 {len(aircraft_objects) if aircraft_objects else 0} 架飞机")
                return basic_scene_info
            
        except Exception as e:
            logger.error(f"生成场景 {scene_id} 失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"错误详情: {error_details}")

            # 保存错误信息到文件以便调试
            try:
                with open(f"logs/scene_error_{scene_id}.log", "w") as f:
                    f.write(f"Scene {scene_id} generation failed\n")
                    f.write(f"Error: {e}\n")
                    f.write(f"Traceback:\n{error_details}\n")
            except:
                pass

            # 即使发生异常，也返回基本场景信息
            basic_scene_info = {
                'scene_id': scene_id,
                'timestamp': 0,
                'ground': {
                    'object_name': f'Ground_{scene_id}',
                    'bounds': (-500, 500, -500, 500),
                    'size': [1000, 1000]
                },
                'aircraft': [],
                'aircraft_count': 0,
                'aircraft_types': [],
                'scene_bounds': {
                    'min_x': -500, 'max_x': 500,
                    'min_y': -500, 'max_y': 500,
                    'min_z': 0, 'max_z': 50
                }
            }
            logger.info(f"场景 {scene_id} 生成失败，返回基本信息")
            return basic_scene_info

    def _generate_mock_scene_info(self, scene_id: int) -> Dict:
        """
        生成模拟场景信息（用于测试环境）

        Args:
            scene_id: 场景ID

        Returns:
            模拟场景信息
        """
        mock_aircraft = []
        num_aircraft = random.randint(1, 5)  # 确保至少有1架飞机
        for i in range(num_aircraft):
            # 生成飞机位置
            location = [random.uniform(-100, 100), random.uniform(-100, 100), random.uniform(0, 50)]

            # 飞机尺寸
            dimensions = {'length': 20, 'width': 15, 'height': 5}

            # 根据位置和尺寸计算世界坐标边界框
            half_length = dimensions['length'] / 2
            half_width = dimensions['width'] / 2
            half_height = dimensions['height'] / 2

            bbox_world = {
                'min_x': location[0] - half_length,
                'max_x': location[0] + half_length,
                'min_y': location[1] - half_width,
                'max_y': location[1] + half_width,
                'min_z': location[2] - half_height,
                'max_z': location[2] + half_height
            }

            # 随机选择飞机类型
            aircraft_types = ['F16', 'Boeing737', 'Boeing747', 'F22', 'SR71', 'X35']
            aircraft_type = random.choice(aircraft_types)

            # 根据类型设置category_id
            type_to_category = {
                'Boeing737': 1, 'Boeing747': 2, 'F16': 3,
                'F22': 4, 'RQ180': 5, 'SR71': 6, 'X35': 7
            }
            category_id = type_to_category.get(aircraft_type, 3)

            aircraft = {
                'name': f'{aircraft_type}_{i+1:03d}',
                'aircraft_type': aircraft_type,
                'category_id': category_id,
                'instance_id': i+1,
                'location': location,
                'rotation': [0, 0, random.uniform(0, 6.28)],
                'bbox_world': bbox_world,
                'dimensions': dimensions
            }

            mock_aircraft.append(aircraft)

        return {
            'scene_id': scene_id,
            'timestamp': 0,
            'ground': {
                'object_name': f'Ground_{scene_id}',
                'bounds': (-500, 500, -500, 500),
                'size': [1000, 1000]
            },
            'aircraft': mock_aircraft,
            'aircraft_count': len(mock_aircraft),
            'aircraft_types': ['F16'],
            'scene_bounds': {
                'min_x': -500, 'max_x': 500,
                'min_y': -500, 'max_y': 500,
                'min_z': 0, 'max_z': 50
            }
        }
    
    def _collect_scene_info(self, scene_id: int, ground_obj,
                           aircraft_objects) -> Dict:
        """
        收集场景信息

        Args:
            scene_id: 场景ID
            ground_obj: 地面对象
            aircraft_objects: 飞机对象列表

        Returns:
            场景信息字典
        """
        try:
            # 获取飞机信息
            aircraft_info = []
            if aircraft_objects:
                try:
                    aircraft_info = self.aircraft_placer.get_aircraft_info(aircraft_objects)
                except Exception as e:
                    logger.warning(f"获取飞机信息失败: {e}")
                    aircraft_info = []

            # 获取地面信息
            ground_bounds = (-500, 500, -500, 500)  # 默认边界
            try:
                ground_bounds = self.ground_creator.get_ground_bounds()
            except Exception as e:
                logger.warning(f"获取地面边界失败: {e}")

            # 构建场景信息
            scene_info = {
                'scene_id': scene_id,
                'timestamp': 0,  # 使用默认值而不是bpy.context.scene.frame_current
                'ground': {
                    'object_name': ground_obj.name if ground_obj else f'Ground_{scene_id}',
                    'bounds': ground_bounds,
                    'size': getattr(self.ground_creator, 'ground_size', [1000, 1000])
                },
                'aircraft': aircraft_info,
                'aircraft_count': len(aircraft_objects) if aircraft_objects else 0,
                'aircraft_types': list(set(info.get('aircraft_type', 'Unknown') for info in aircraft_info)) if aircraft_info else [],
                'scene_bounds': self._calculate_scene_bounds(aircraft_info, ground_bounds)
            }

            logger.info(f"场景信息收集成功: {len(scene_info)} 个键")
            return scene_info

        except Exception as e:
            logger.error(f"收集场景信息时发生异常: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def _calculate_scene_bounds(self, aircraft_info: List[Dict], 
                               ground_bounds: Tuple[float, float, float, float]) -> Dict:
        """
        计算场景边界
        
        Args:
            aircraft_info: 飞机信息列表
            ground_bounds: 地面边界
            
        Returns:
            场景边界字典
        """
        if not aircraft_info:
            min_x, max_x, min_y, max_y = ground_bounds
            return {
                'min_x': min_x, 'max_x': max_x,
                'min_y': min_y, 'max_y': max_y,
                'min_z': 0, 'max_z': 0
            }
            
        # 计算所有飞机的边界
        all_min_x = min(info['bbox_world']['min_x'] for info in aircraft_info)
        all_max_x = max(info['bbox_world']['max_x'] for info in aircraft_info)
        all_min_y = min(info['bbox_world']['min_y'] for info in aircraft_info)
        all_max_y = max(info['bbox_world']['max_y'] for info in aircraft_info)
        all_min_z = min(info['bbox_world']['min_z'] for info in aircraft_info)
        all_max_z = max(info['bbox_world']['max_z'] for info in aircraft_info)
        
        # 与地面边界合并
        ground_min_x, ground_max_x, ground_min_y, ground_max_y = ground_bounds
        
        return {
            'min_x': min(all_min_x, ground_min_x),
            'max_x': max(all_max_x, ground_max_x),
            'min_y': min(all_min_y, ground_min_y),
            'max_y': max(all_max_y, ground_max_y),
            'min_z': min(all_min_z, 0),  # 地面高度为0
            'max_z': all_max_z
        }
    
    def get_current_scene_info(self) -> Optional[Dict]:
        """
        获取当前场景信息
        
        Returns:
            场景信息字典
        """
        return self.current_scene_info
    
    def save_scene_file(self, filepath: str) -> bool:
        """
        保存场景文件
        
        Args:
            filepath: 保存路径
            
        Returns:
            是否成功
        """
        try:
            bpy.ops.wm.save_as_mainfile(filepath=filepath)
            logger.info(f"场景文件已保存: {filepath}")
            return True
        except Exception as e:
            logger.error(f"保存场景文件失败: {e}")
            return False
    
    def load_scene_file(self, filepath: str) -> bool:
        """
        加载场景文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            是否成功
        """
        try:
            bpy.ops.wm.open_mainfile(filepath=filepath)
            logger.info(f"场景文件已加载: {filepath}")
            return True
        except Exception as e:
            logger.error(f"加载场景文件失败: {e}")
            return False
    
    def generate_batch_scenes(self, count: int, base_seed: int = 42) -> List[Dict]:
        """
        批量生成场景
        
        Args:
            count: 生成数量
            base_seed: 基础随机种子
            
        Returns:
            场景信息列表
        """
        scenes_info = []
        
        for i in range(count):
            scene_seed = base_seed + i
            scene_info = self.generate_scene(i + 1, scene_seed)
            
            if scene_info:
                scenes_info.append(scene_info)
            else:
                logger.error(f"场景 {i + 1} 生成失败")
                
        logger.info(f"批量生成完成: {len(scenes_info)}/{count} 个场景")
        return scenes_info
    
    def validate_scene(self, scene_info: Dict) -> bool:
        """
        验证场景有效性
        
        Args:
            scene_info: 场景信息
            
        Returns:
            是否有效
        """
        if not scene_info:
            return False
            
        # 检查基本信息
        required_keys = ['scene_id', 'ground', 'aircraft', 'aircraft_count']
        for key in required_keys:
            if key not in scene_info:
                logger.error(f"场景信息缺少必需字段: {key}")
                return False
                
        # 检查飞机数量
        if scene_info['aircraft_count'] == 0:
            logger.error("场景中没有飞机")
            return False
            
        # 检查飞机信息完整性
        for aircraft in scene_info['aircraft']:
            required_aircraft_keys = ['name', 'aircraft_type', 'category_id', 'location']
            for key in required_aircraft_keys:
                if key not in aircraft:
                    logger.error(f"飞机信息缺少必需字段: {key}")
                    return False
                    
        return True
