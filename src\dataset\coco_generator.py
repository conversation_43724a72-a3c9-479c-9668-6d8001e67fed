"""
COCO格式数据集生成器
"""
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

try:
    from ..utils import config
    from .annotation_creator import AnnotationCreator
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config
    from dataset.annotation_creator import AnnotationCreator

logger = logging.getLogger(__name__)


class COCOGenerator:
    """COCO格式数据集生成器"""
    
    def __init__(self):
        """初始化COCO生成器"""
        self.annotation_creator = AnnotationCreator()
        
        # 从配置获取飞机类别信息
        self.aircraft_models = config.get('aircraft.models', [])
        self.categories = self._create_categories()
        
        # COCO数据结构
        self.coco_data = {
            'info': self._create_info(),
            'licenses': self._create_licenses(),
            'categories': self.categories,
            'images': [],
            'annotations': []
        }
        
        # 计数器
        self.image_id_counter = 1
        self.annotation_id_counter = 1
        
    def _create_info(self) -> Dict:
        """创建数据集信息"""
        return {
            'year': datetime.now().year,
            'version': '1.0',
            'description': 'SAR Aircraft Detection Dataset',
            'contributor': 'SAR Simulation Team',
            'url': '',
            'date_created': datetime.now().isoformat()
        }
    
    def _create_licenses(self) -> List[Dict]:
        """创建许可证信息"""
        return [
            {
                'id': 1,
                'name': 'Custom License',
                'url': ''
            }
        ]
    
    def _create_categories(self) -> List[Dict]:
        """创建类别信息"""
        categories = []
        
        for model in self.aircraft_models:
            category = {
                'id': model['category_id'],
                'name': model['name'],
                'supercategory': 'aircraft'
            }
            categories.append(category)
        
        logger.info(f"创建了 {len(categories)} 个类别")
        return categories
    
    def add_image_and_annotations(self, image_path: str, 
                                scene_info: Dict,
                                camera_params: Dict,
                                image_size: Tuple[int, int]) -> bool:
        """
        添加图像和对应的标注
        
        Args:
            image_path: 图像文件路径
            scene_info: 场景信息
            camera_params: 相机参数
            image_size: 图像尺寸 (width, height)
            
        Returns:
            是否成功添加
        """
        try:
            # 创建图像信息
            image_info = self._create_image_info(image_path, image_size)
            
            # 创建标注
            annotations = self.annotation_creator.create_annotations_from_scene(
                scene_info, camera_params, image_size
            )
            
            # 过滤和验证标注
            annotations = self.annotation_creator.filter_annotations_by_visibility(annotations)
            annotations = self.annotation_creator.validate_annotations(annotations, image_size)
            
            if not annotations:
                logger.warning(f"图像 {image_path} 没有有效标注")
                return False
            
            # 转换为COCO格式的标注
            coco_annotations = []
            for annotation in annotations:
                coco_ann = self._create_coco_annotation(annotation, self.image_id_counter)
                coco_annotations.append(coco_ann)
            
            # 添加到数据集
            self.coco_data['images'].append(image_info)
            self.coco_data['annotations'].extend(coco_annotations)
            
            # 更新计数器
            self.image_id_counter += 1
            self.annotation_id_counter += len(coco_annotations)
            
            logger.debug(f"添加图像: {image_path}, 标注数: {len(coco_annotations)}")
            return True
            
        except Exception as e:
            logger.error(f"添加图像和标注失败 {image_path}: {e}")
            return False
    
    def _create_image_info(self, image_path: str, 
                          image_size: Tuple[int, int]) -> Dict:
        """
        创建图像信息
        
        Args:
            image_path: 图像路径
            image_size: 图像尺寸
            
        Returns:
            图像信息字典
        """
        width, height = image_size
        filename = os.path.basename(image_path)
        
        image_info = {
            'id': self.image_id_counter,
            'width': width,
            'height': height,
            'file_name': filename,
            'license': 1,
            'flickr_url': '',
            'coco_url': '',
            'date_captured': datetime.now().isoformat()
        }
        
        return image_info
    
    def _create_coco_annotation(self, annotation: Dict, image_id: int) -> Dict:
        """
        创建COCO格式的标注
        
        Args:
            annotation: 原始标注
            image_id: 图像ID
            
        Returns:
            COCO格式标注
        """
        bbox = annotation['bbox']  # [x, y, width, height]
        area = bbox[2] * bbox[3]
        
        # 创建分割掩码（简化为边界框）
        segmentation = [[
            bbox[0], bbox[1],  # 左上
            bbox[0] + bbox[2], bbox[1],  # 右上
            bbox[0] + bbox[2], bbox[1] + bbox[3],  # 右下
            bbox[0], bbox[1] + bbox[3]  # 左下
        ]]
        
        coco_annotation = {
            'id': self.annotation_id_counter,
            'image_id': image_id,
            'category_id': annotation['category_id'],
            'segmentation': segmentation,
            'area': area,
            'bbox': bbox,
            'iscrowd': annotation.get('iscrowd', 0),
            'attributes': {
                'aircraft_name': annotation.get('aircraft_name', ''),
                'aircraft_type': annotation.get('aircraft_type', ''),
                'instance_id': annotation.get('instance_id', 0),
                'world_location': annotation.get('world_location', []),
                'world_rotation': annotation.get('world_rotation', []),
                'dimensions': annotation.get('dimensions', {})
            }
        }
        
        self.annotation_id_counter += 1
        return coco_annotation
    
    def save_coco_dataset(self, output_path: str) -> bool:
        """
        保存COCO数据集
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否成功保存
        """
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.coco_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"COCO数据集已保存: {output_path}")
            logger.info(f"图像数: {len(self.coco_data['images'])}, "
                       f"标注数: {len(self.coco_data['annotations'])}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存COCO数据集失败: {e}")
            return False
    
    def get_dataset_statistics(self) -> Dict:
        """
        获取数据集统计信息
        
        Returns:
            统计信息字典
        """
        images = self.coco_data['images']
        annotations = self.coco_data['annotations']
        categories = self.coco_data['categories']
        
        # 统计每个类别的实例数
        category_counts = {}
        for category in categories:
            category_counts[category['name']] = 0
        
        for annotation in annotations:
            category_id = annotation['category_id']
            category_name = next(cat['name'] for cat in categories if cat['id'] == category_id)
            category_counts[category_name] += 1
        
        # 计算边界框尺寸统计
        bbox_areas = [ann['area'] for ann in annotations]
        bbox_widths = [ann['bbox'][2] for ann in annotations]
        bbox_heights = [ann['bbox'][3] for ann in annotations]
        
        statistics = {
            'total_images': len(images),
            'total_annotations': len(annotations),
            'total_categories': len(categories),
            'category_counts': category_counts,
            'annotations_per_image': len(annotations) / len(images) if images else 0,
            'bbox_statistics': {
                'mean_area': sum(bbox_areas) / len(bbox_areas) if bbox_areas else 0,
                'mean_width': sum(bbox_widths) / len(bbox_widths) if bbox_widths else 0,
                'mean_height': sum(bbox_heights) / len(bbox_heights) if bbox_heights else 0,
                'min_area': min(bbox_areas) if bbox_areas else 0,
                'max_area': max(bbox_areas) if bbox_areas else 0
            }
        }
        
        return statistics
    
    def validate_dataset(self) -> bool:
        """
        验证数据集的完整性
        
        Returns:
            数据集是否有效
        """
        try:
            # 检查基本结构
            required_keys = ['info', 'licenses', 'categories', 'images', 'annotations']
            for key in required_keys:
                if key not in self.coco_data:
                    logger.error(f"缺少必需字段: {key}")
                    return False
            
            # 检查类别
            if not self.coco_data['categories']:
                logger.error("没有定义类别")
                return False
            
            category_ids = set(cat['id'] for cat in self.coco_data['categories'])
            
            # 检查图像
            if not self.coco_data['images']:
                logger.error("没有图像")
                return False
            
            image_ids = set(img['id'] for img in self.coco_data['images'])
            
            # 检查标注
            for annotation in self.coco_data['annotations']:
                # 检查图像ID引用
                if annotation['image_id'] not in image_ids:
                    logger.error(f"标注引用了不存在的图像ID: {annotation['image_id']}")
                    return False
                
                # 检查类别ID引用
                if annotation['category_id'] not in category_ids:
                    logger.error(f"标注引用了不存在的类别ID: {annotation['category_id']}")
                    return False
                
                # 检查边界框格式
                bbox = annotation['bbox']
                if len(bbox) != 4 or any(v < 0 for v in bbox):
                    logger.error(f"边界框格式错误: {bbox}")
                    return False
            
            logger.info("数据集验证通过")
            return True
            
        except Exception as e:
            logger.error(f"数据集验证失败: {e}")
            return False
    
    def reset(self):
        """重置数据集"""
        self.coco_data = {
            'info': self._create_info(),
            'licenses': self._create_licenses(),
            'categories': self.categories,
            'images': [],
            'annotations': []
        }
        
        self.image_id_counter = 1
        self.annotation_id_counter = 1
        
        logger.info("COCO数据集已重置")
    
    def merge_datasets(self, other_coco_file: str) -> bool:
        """
        合并另一个COCO数据集
        
        Args:
            other_coco_file: 另一个COCO数据集文件路径
            
        Returns:
            是否成功合并
        """
        try:
            with open(other_coco_file, 'r', encoding='utf-8') as f:
                other_data = json.load(f)
            
            # 获取当前最大ID
            max_image_id = max([img['id'] for img in self.coco_data['images']], default=0)
            max_annotation_id = max([ann['id'] for ann in self.coco_data['annotations']], default=0)
            
            # 更新图像ID并合并
            for image in other_data['images']:
                image['id'] += max_image_id
                self.coco_data['images'].append(image)
            
            # 更新标注ID和图像ID引用并合并
            for annotation in other_data['annotations']:
                annotation['id'] += max_annotation_id
                annotation['image_id'] += max_image_id
                self.coco_data['annotations'].append(annotation)
            
            # 更新计数器
            self.image_id_counter = max([img['id'] for img in self.coco_data['images']]) + 1
            self.annotation_id_counter = max([ann['id'] for ann in self.coco_data['annotations']]) + 1
            
            logger.info(f"成功合并数据集: {other_coco_file}")
            return True
            
        except Exception as e:
            logger.error(f"合并数据集失败: {e}")
            return False
