# SAR图像仿真数据集生成器

基于Blender的合成孔径雷达(SAR)图像仿真数据集生成项目，用于生成高质量的SAR图像训练数据。

## 功能特性

- 🛩️ 支持多种飞机3D模型的自动加载和标准化
- 🌍 智能场景生成，支持随机飞机放置和参数控制
- 📡 物理准确的SAR成像仿真（RD算法）
- 🎯 自动目标检测和COCO格式数据集生成
- 📊 均匀的训练/验证/测试集分割
- ⚡ 高效的批量处理和并行化支持

## 系统要求

- Python 3.8+
- Blender 4.5+
- 8GB+ RAM
- NVIDIA GPU（推荐，用于加速渲染）

## 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd SAR_Sim
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 配置Blender路径：
编辑 `config.yaml` 文件中的 `blender.executable_path` 为您的Blender安装路径。

## 🚀 快速开始

### 生成示例数据集
```bash
python examples/basic_usage.py --demo basic
```

### 运行测试
```bash
python run_tests.py
```

## 使用方法

### 基本用法

```bash
python main.py
```

### 自定义参数

```bash
python main.py --config custom_config.yaml --output ./my_dataset --num-images 5000
```

### 参数说明

- `--config`: 配置文件路径（默认：config.yaml）
- `--output`: 输出目录
- `--num-images`: 生成图像数量
- `--headless`: 无头模式运行Blender
- `--verbose`: 详细输出

## 配置文件

主要配置项说明：

### 数据集配置
- `image_size`: 输出图像尺寸
- `total_images`: 总图像数量
- `train_ratio/val_ratio/test_ratio`: 数据集分割比例

### 飞机模型配置
- `models`: 飞机模型列表，包含名称、文件路径、类别ID和真实长度
- `standard_length`: 标准化长度，所有飞机将缩放到此长度

### 场景配置
- `ground_size`: 地面场景大小
- `aircraft_count`: 每个场景中的飞机数量范围
- `placement`: 飞机放置约束（高度、角度等）

### SAR成像参数
- `frequency`: 雷达频率
- `bandwidth`: 信号带宽
- `range_resolution/azimuth_resolution`: 分辨率设置
- `noise`: 噪声参数配置

## 项目结构

```
SAR_Sim/
├── assets/
│   └── jets/                 # 飞机3D模型文件
├── src/
│   ├── models/              # 3D模型管理
│   ├── scene/               # 场景生成
│   ├── camera/              # 相机控制
│   ├── sar/                 # SAR成像仿真
│   ├── dataset/             # 数据集生成
│   └── utils/               # 工具模块
├── output/                  # 输出目录
├── logs/                    # 日志文件
├── config.yaml             # 配置文件
├── requirements.txt        # 依赖列表
└── main.py                 # 主程序
```

## 输出格式

生成的数据集包含：

1. **图像文件**: PNG格式的SAR图像
2. **COCO标注文件**: 包含目标检测标注的JSON文件
3. **元数据**: 场景参数、SAR成像参数等

### COCO格式说明

```json
{
  "images": [...],           # 图像信息
  "annotations": [...],      # 标注信息
  "categories": [            # 类别信息
    {"id": 1, "name": "Boeing737"},
    {"id": 2, "name": "Boeing747"},
    ...
  ]
}
```

## 技术原理

### SAR成像仿真

本项目实现了基于Range-Doppler (RD)算法的SAR成像仿真：

1. **雷达信号建模**: 模拟X波段雷达信号
2. **回波仿真**: 计算目标的雷达散射截面(RCS)
3. **成像处理**: RD算法进行距离-多普勒成像
4. **噪声添加**: 添加热噪声、杂波和斑点噪声

### 物理准确性

- 使用真实的雷达参数和材质属性
- 考虑目标的几何形状和材质对RCS的影响
- 模拟真实的SAR成像几何关系

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
