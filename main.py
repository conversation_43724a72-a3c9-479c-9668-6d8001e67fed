#!/usr/bin/env python3
"""
SAR图像仿真数据集生成主程序
"""
import os
import sys
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils import config, setup_logger


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SAR图像仿真数据集生成器")
    parser.add_argument("--config", default="config.yaml", help="配置文件路径")
    parser.add_argument("--output", default=None, help="输出目录")
    parser.add_argument("--num-images", type=int, default=None, help="生成图像数量")
    parser.add_argument("--headless", action="store_true", help="无头模式运行Blender")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger("sar_sim", log_level)
    
    try:
        # 加载配置
        config.config_path = args.config
        if not config.validate_config():
            logger.error("配置文件验证失败")
            return 1
            
        logger.info("配置文件验证成功")
        
        # 准备配置覆盖
        config_overrides = {}
        if args.output:
            config_overrides['dataset.output_dir'] = args.output
        if args.headless:
            config_overrides['blender.headless'] = True
            
        # 创建输出目录
        output_dir = config.get('dataset.output_dir')
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"开始生成SAR数据集，输出目录: {output_dir}")

        # 使用BlenderRunner在Blender环境中执行数据集生成
        from utils import blender_runner

        # 检查Blender是否可用
        if not blender_runner.is_blender_available():
            logger.error(f"Blender不可用，路径: {blender_runner.blender_path}")
            logger.error("请检查config.yaml中的blender.executable_path配置")
            return 1

        logger.info(f"使用Blender: {blender_runner.blender_path}")

        # 生成数据集
        num_images = args.num_images if args.num_images else config.get('dataset.total_images')
        success = blender_runner.run_dataset_generation(
            num_images=num_images,
            config_overrides=config_overrides
        )

        if not success:
            logger.error("数据集生成失败")
            return 1

        logger.info("数据集生成完成")
        return 0
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1


if __name__ == "__main__":
    import logging
    sys.exit(main())
