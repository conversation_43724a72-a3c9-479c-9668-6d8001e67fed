"""
配置文件加载器
"""
import yaml
import os
from typing import Dict, Any


class ConfigLoader:
    """配置文件加载器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置加载器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self._config = None
        
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if self._config is None:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
                # 转换科学记数法字符串为数字
                self._config = self._convert_scientific_notation(self._config)
                
        return self._config
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键，支持点分隔的嵌套键，如 'dataset.image_size'
            default: 默认值
            
        Returns:
            配置值
        """
        config = self.load_config()
        keys = key.split('.')
        
        value = config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
    
    def validate_config(self) -> bool:
        """
        验证配置文件的有效性
        
        Returns:
            配置是否有效
        """
        try:
            config = self.load_config()
            
            # 检查必需的配置项
            required_keys = [
                'blender.executable_path',
                'dataset.output_dir',
                'dataset.image_size',
                'aircraft.models_dir',
                'aircraft.models',
                'scene.ground_size',
                'sar.frequency'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    print(f"缺少必需的配置项: {key}")
                    return False
                    
            # 检查Blender可执行文件是否存在
            blender_path = self.get('blender.executable_path')
            if not os.path.exists(blender_path):
                print(f"Blender可执行文件不存在: {blender_path}")
                return False
                
            # 检查飞机模型目录是否存在
            models_dir = self.get('aircraft.models_dir')
            if not os.path.exists(models_dir):
                print(f"飞机模型目录不存在: {models_dir}")
                return False
                
            return True

        except Exception as e:
            print(f"配置验证失败: {e}")
            return False

    def _convert_scientific_notation(self, obj):
        """
        递归转换配置中的科学记数法字符串为数字

        Args:
            obj: 要转换的对象

        Returns:
            转换后的对象
        """
        if isinstance(obj, dict):
            return {key: self._convert_scientific_notation(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_scientific_notation(item) for item in obj]
        elif isinstance(obj, str):
            # 尝试转换科学记数法字符串
            try:
                if 'e' in obj.lower() and obj.replace('.', '').replace('-', '').replace('+', '').replace('e', '').isdigit():
                    return float(obj)
            except (ValueError, AttributeError):
                pass
            return obj
        else:
            return obj


# 全局配置实例
config = ConfigLoader()
