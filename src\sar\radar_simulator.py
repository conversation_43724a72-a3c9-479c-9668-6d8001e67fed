"""
雷达信号仿真器
"""
import numpy as np
import math
from typing import Dict, Tuple, Optional
import logging

try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config

logger = logging.getLogger(__name__)


class RadarSimulator:
    """雷达信号仿真器，模拟SAR雷达的信号特性"""
    
    def __init__(self):
        """初始化雷达仿真器"""
        # 从配置获取雷达参数
        sar_config = config.get('sar', {})

        # 确保配置值是数字类型
        self.frequency = float(sar_config.get('frequency', 9.6e9))  # X波段频率 (Hz)
        self.bandwidth = float(sar_config.get('bandwidth', 600e6))  # 带宽 (Hz)
        self.pulse_duration = float(sar_config.get('pulse_duration', 1e-6))  # 脉冲持续时间 (s)
        self.prf = float(sar_config.get('prf', 1000))  # 脉冲重复频率 (Hz)
        
        # 计算派生参数
        self.wavelength = 3e8 / self.frequency  # 波长 (m)
        self.range_resolution = float(sar_config.get('range_resolution', 0.25))  # 距离分辨率 (m)
        self.azimuth_resolution = float(sar_config.get('azimuth_resolution', 0.25))  # 方位分辨率 (m)
        
        # 物理常数
        self.c = 3e8  # 光速 (m/s)
        
        logger.info(f"雷达参数初始化 - 频率: {self.frequency/1e9:.2f} GHz, "
                   f"带宽: {self.bandwidth/1e6:.0f} MHz, 波长: {self.wavelength*100:.2f} cm")
    
    def calculate_range_time(self, distance: float) -> float:
        """
        计算距离对应的时间延迟
        
        Args:
            distance: 距离 (m)
            
        Returns:
            时间延迟 (s)
        """
        return 2 * distance / self.c  # 双程时间
    
    def calculate_doppler_frequency(self, relative_velocity: float) -> float:
        """
        计算多普勒频率
        
        Args:
            relative_velocity: 相对速度 (m/s)，正值表示接近
            
        Returns:
            多普勒频率 (Hz)
        """
        return 2 * relative_velocity / self.wavelength
    
    def generate_chirp_signal(self, duration: Optional[float] = None, 
                             sampling_rate: float = 1e9) -> np.ndarray:
        """
        生成线性调频信号（Chirp）
        
        Args:
            duration: 信号持续时间，默认使用脉冲持续时间
            sampling_rate: 采样率 (Hz)
            
        Returns:
            复数信号数组
        """
        if duration is None:
            duration = self.pulse_duration
            
        # 时间轴
        t = np.arange(0, duration, 1/sampling_rate)
        
        # 线性调频率
        chirp_rate = self.bandwidth / duration
        
        # 生成复数chirp信号
        signal = np.exp(1j * 2 * np.pi * (self.frequency * t + 0.5 * chirp_rate * t**2))
        
        return signal
    
    def calculate_path_loss(self, distance: float) -> float:
        """
        计算自由空间路径损耗
        
        Args:
            distance: 距离 (m)
            
        Returns:
            路径损耗 (dB)
        """
        if distance <= 0:
            return 0
            
        # 自由空间路径损耗公式: L = 20*log10(4*pi*R/lambda)
        path_loss_db = 20 * math.log10(4 * math.pi * distance / self.wavelength)
        return path_loss_db
    
    def calculate_radar_equation(self, target_rcs: float, distance: float, 
                                transmit_power: float = 1000.0) -> float:
        """
        计算雷达方程，得到接收功率
        
        Args:
            target_rcs: 目标雷达散射截面 (m²)
            distance: 距离 (m)
            transmit_power: 发射功率 (W)
            
        Returns:
            接收功率 (W)
        """
        if distance <= 0 or target_rcs <= 0:
            return 0
            
        # 简化的雷达方程: Pr = Pt * G² * λ² * σ / ((4π)³ * R⁴)
        # 假设天线增益 G = 1000 (30 dB)
        antenna_gain = 1000
        
        numerator = transmit_power * (antenna_gain**2) * (self.wavelength**2) * target_rcs
        denominator = ((4 * math.pi)**3) * (distance**4)
        
        received_power = numerator / denominator
        
        return received_power
    
    def simulate_target_response(self, target_info: Dict, radar_position: Tuple[float, float, float],
                                platform_velocity: Tuple[float, float, float] = (0, 0, 0)) -> Dict:
        """
        仿真目标的雷达响应
        
        Args:
            target_info: 目标信息字典，包含位置、RCS等
            radar_position: 雷达位置 (x, y, z)
            platform_velocity: 平台速度 (vx, vy, vz)
            
        Returns:
            目标响应信息
        """
        # 计算目标到雷达的距离
        target_pos = target_info.get('position', (0, 0, 0))
        dx = target_pos[0] - radar_position[0]
        dy = target_pos[1] - radar_position[1]
        dz = target_pos[2] - radar_position[2]
        
        distance = math.sqrt(dx**2 + dy**2 + dz**2)
        
        # 计算相对速度（简化为径向速度）
        # 假设目标静止，只考虑平台运动
        if distance > 0:
            # 单位方向向量
            unit_vector = (dx/distance, dy/distance, dz/distance)
            # 径向速度分量
            radial_velocity = (platform_velocity[0] * unit_vector[0] + 
                             platform_velocity[1] * unit_vector[1] + 
                             platform_velocity[2] * unit_vector[2])
        else:
            radial_velocity = 0
        
        # 计算时间延迟和多普勒频率
        time_delay = self.calculate_range_time(distance)
        doppler_freq = self.calculate_doppler_frequency(radial_velocity)
        
        # 计算接收功率
        target_rcs = target_info.get('rcs', 1.0)
        received_power = self.calculate_radar_equation(target_rcs, distance)
        
        # 计算路径损耗
        path_loss = self.calculate_path_loss(distance)
        
        response = {
            'target_id': target_info.get('id', 'unknown'),
            'distance': distance,
            'time_delay': time_delay,
            'doppler_frequency': doppler_freq,
            'radial_velocity': radial_velocity,
            'received_power': received_power,
            'received_power_db': 10 * math.log10(received_power) if received_power > 0 else -np.inf,
            'path_loss_db': path_loss,
            'rcs': target_rcs,
            'azimuth_angle': math.atan2(dy, dx),  # 方位角
            'elevation_angle': math.atan2(dz, math.sqrt(dx**2 + dy**2))  # 俯仰角
        }
        
        return response
    
    def generate_range_profile(self, targets_response: list, 
                              range_bins: int = 1024) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成距离剖面
        
        Args:
            targets_response: 目标响应列表
            range_bins: 距离单元数
            
        Returns:
            (距离轴, 距离剖面)
        """
        # 计算距离轴
        max_range = max([resp['distance'] for resp in targets_response]) if targets_response else 1000
        range_axis = np.linspace(0, max_range * 1.2, range_bins)
        range_resolution_bins = self.range_resolution / (max_range * 1.2) * range_bins
        
        # 初始化距离剖面
        range_profile = np.zeros(range_bins, dtype=complex)
        
        for response in targets_response:
            # 计算目标在距离轴上的位置
            range_bin = int(response['distance'] / (max_range * 1.2) * range_bins)
            
            if 0 <= range_bin < range_bins:
                # 添加目标响应（考虑功率和相位）
                amplitude = math.sqrt(response['received_power'])
                phase = 2 * math.pi * response['distance'] / self.wavelength
                
                # 使用sinc函数模拟距离分辨率
                for i in range(max(0, range_bin - int(range_resolution_bins)), 
                              min(range_bins, range_bin + int(range_resolution_bins) + 1)):
                    sinc_arg = (i - range_bin) / range_resolution_bins
                    sinc_value = np.sinc(sinc_arg) if sinc_arg != 0 else 1.0
                    range_profile[i] += amplitude * sinc_value * np.exp(1j * phase)
        
        return range_axis, range_profile
    
    def get_radar_parameters(self) -> Dict:
        """
        获取雷达参数
        
        Returns:
            雷达参数字典
        """
        return {
            'frequency': self.frequency,
            'wavelength': self.wavelength,
            'bandwidth': self.bandwidth,
            'pulse_duration': self.pulse_duration,
            'prf': self.prf,
            'range_resolution': self.range_resolution,
            'azimuth_resolution': self.azimuth_resolution,
            'c': self.c
        }
