"""
SAR成像主模块
"""
import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
import logging

from .radar_simulator import RadarSimulator
from .rcs_calculator import RCSCalculator
from .rd_processor import RDProcessor
from .noise_generator import NoiseGenerator
try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config

logger = logging.getLogger(__name__)


class SARImaging:
    """SAR成像主模块，集成所有SAR仿真组件"""
    
    def __init__(self):
        """初始化SAR成像模块"""
        # 初始化各个组件
        self.radar_simulator = RadarSimulator()
        self.rcs_calculator = RCSCalculator()
        self.rd_processor = RDProcessor(self.radar_simulator.get_radar_parameters())
        self.noise_generator = NoiseGenerator()
        
        # 获取图像参数
        self.image_size = config.get('dataset.image_size', [512, 512])
        
    def simulate_sar_image_from_scene(self, scene_info: Dict,
                                     camera_params: Dict,
                                     noise_params: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """
        从场景信息生成SAR图像
        
        Args:
            scene_info: 场景信息字典
            camera_params: 相机参数字典
            noise_params: 噪声参数字典
            
        Returns:
            (SAR图像, 成像信息)
        """
        logger.info(f"开始生成场景 {scene_info.get('scene_id', 'unknown')} 的SAR图像")
        
        try:
            # 1. 计算场景的RCS分布
            rcs_map = self._calculate_scene_rcs_distribution(scene_info, camera_params)
            
            # 2. 仿真雷达回波数据
            raw_echo_data = self._simulate_radar_echo(rcs_map, scene_info, camera_params)
            
            # 3. SAR成像处理
            sar_image_complex = self._process_sar_imaging(raw_echo_data)
            
            # 4. 转换为幅度图像
            sar_image_magnitude = np.abs(sar_image_complex)
            
            # 5. 添加噪声
            if noise_params is None:
                noise_params = {}
            noisy_sar_image = self.noise_generator.generate_realistic_sar_noise(
                sar_image_magnitude, noise_params
            )
            
            # 6. 图像后处理
            logger.debug(f"噪声SAR图像统计: min={np.min(noisy_sar_image):.6f}, "
                        f"max={np.max(noisy_sar_image):.6f}, "
                        f"mean={np.mean(noisy_sar_image):.6f}")
            final_image = self._post_process_image(noisy_sar_image)
            
            # 7. 收集成像信息
            imaging_info = self._collect_imaging_info(scene_info, camera_params, 
                                                    sar_image_complex, final_image)
            
            if final_image is not None:
                logger.info(f"SAR图像生成完成，尺寸: {final_image.shape}")
            else:
                logger.warning("SAR图像生成返回None")
            return final_image, imaging_info
            
        except Exception as e:
            import traceback
            logger.error(f"SAR图像生成失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            # 返回空白图像
            empty_image = np.zeros(self.image_size, dtype=np.uint8)
            return empty_image, {}
    
    def _calculate_scene_rcs_distribution(self, scene_info: Dict,
                                        camera_params: Dict) -> np.ndarray:
        """
        计算场景的RCS分布

        Args:
            scene_info: 场景信息
            camera_params: 相机参数

        Returns:
            RCS分布图
        """
        # 从场景信息获取世界坐标边界
        scene_bounds = scene_info.get('scene_bounds', {})
        if not scene_bounds:
            # 使用默认边界
            scene_bounds = {
                'min_x': -500, 'max_x': 500,
                'min_y': -500, 'max_y': 500,
                'min_z': 0, 'max_z': 50
            }

        # 创建虚拟的对象列表用于RCS计算
        virtual_objects = []

        # 添加地面RCS（如果有地面信息）
        ground_info = scene_info.get('ground', {})
        if ground_info:
            ground_bounds = ground_info.get('bounds', (-500, 500, -500, 500))
            virtual_ground = {
                'position': [0, 0, 0],
                'bbox': {
                    'min_x': ground_bounds[0], 'max_x': ground_bounds[1],
                    'min_y': ground_bounds[2], 'max_y': ground_bounds[3],
                    'min_z': -1, 'max_z': 1
                },
                'rcs': 0.0001,  # 地面的极低RCS值，进一步降低以确保地面基本为黑色
                'type': 'ground'
            }
            virtual_objects.append(virtual_ground)

        # 添加飞机RCS
        for aircraft in scene_info.get('aircraft', []):
            # 估算飞机的RCS
            aircraft_type = aircraft.get('aircraft_type', 'unknown')
            estimated_rcs = self._estimate_aircraft_rcs(aircraft_type, aircraft)

            virtual_obj = {
                'position': aircraft['location'],
                'bbox': aircraft['bbox_world'],
                'rcs': estimated_rcs,
                'type': aircraft_type
            }
            virtual_objects.append(virtual_obj)

        # 如果没有飞机，创建一些测试目标
        if len([obj for obj in virtual_objects if obj['type'] != 'ground']) == 0:
            logger.warning("场景中没有飞机，创建测试目标")
            test_targets = [
                {
                    'position': [100, 100, 10],
                    'bbox': {'min_x': 90, 'max_x': 110, 'min_y': 90, 'max_y': 110, 'min_z': 5, 'max_z': 15},
                    'rcs': 10.0,
                    'type': 'test_target'
                },
                {
                    'position': [-100, -100, 10],
                    'bbox': {'min_x': -110, 'max_x': -90, 'min_y': -110, 'max_y': -90, 'min_z': 5, 'max_z': 15},
                    'rcs': 5.0,
                    'type': 'test_target'
                }
            ]
            virtual_objects.extend(test_targets)

        # 生成RCS分布图，考虑相机高度的影响
        rcs_map = self._generate_rcs_map_from_virtual_objects(
            virtual_objects, scene_bounds, tuple(self.image_size), camera_params
        )

        logger.debug(f"RCS分布图统计: min={np.min(rcs_map):.6f}, "
                    f"max={np.max(rcs_map):.6f}, "
                    f"mean={np.mean(rcs_map):.6f}, "
                    f"非零元素数量={np.count_nonzero(rcs_map)}")

        return rcs_map
    
    def _estimate_aircraft_rcs(self, aircraft_type: str, aircraft_info: Dict) -> float:
        """
        估算飞机的RCS值
        
        Args:
            aircraft_type: 飞机类型
            aircraft_info: 飞机信息
            
        Returns:
            估算的RCS值
        """
        # 基于飞机类型的基础RCS值（平方米）
        base_rcs_values = {
            'Boeing737': 100.0,   # 大型民航客机
            'Boeing747': 200.0,   # 超大型客机
            'F16': 5.0,           # 战斗机
            'F22': 0.5,           # 隐身战斗机
            'RQ180': 0.1,         # 隐身无人机
            'SR71': 20.0,         # 大型侦察机
            'X35': 3.0            # 战斗机
        }

        base_rcs = base_rcs_values.get(aircraft_type, 20.0)  # 默认较大的RCS值
        
        # 根据飞机尺寸调整RCS
        dimensions = aircraft_info.get('dimensions', {})
        length = dimensions.get('length', 20.0)
        width = dimensions.get('width', 20.0)
        
        # 简单的尺寸修正因子
        size_factor = (length * width) / (20.0 * 20.0)
        
        # 考虑飞机姿态的影响
        rotation = aircraft_info.get('rotation', [0, 0, 0])
        # 简化：只考虑偏航角对RCS的影响
        yaw_angle = abs(rotation[2])
        aspect_factor = 1.0 + 0.5 * np.sin(yaw_angle)  # 侧面RCS通常更大
        
        final_rcs = base_rcs * size_factor * aspect_factor
        
        return max(final_rcs, 0.01)  # 确保最小RCS值
    
    def _generate_rcs_map_from_virtual_objects(self, virtual_objects: List[Dict],
                                             world_bounds: Dict,
                                             image_size: Tuple[int, int],
                                             camera_params: Dict = None) -> np.ndarray:
        """
        从虚拟对象生成RCS分布图

        Args:
            virtual_objects: 虚拟对象列表
            world_bounds: 世界坐标边界
            image_size: 图像尺寸
            camera_params: 相机参数（包含高度等信息）

        Returns:
            RCS分布图
        """
        height, width = image_size
        rcs_map = np.zeros((height, width), dtype=np.float32)
        
        # 计算像素到世界坐标的映射
        world_width = world_bounds['max_x'] - world_bounds['min_x']
        world_height = world_bounds['max_y'] - world_bounds['min_y']
        
        pixel_to_world_x = world_width / width
        pixel_to_world_y = world_height / height

        # 获取相机高度，用于计算视角效应
        camera_height = 1000  # 默认高度
        if camera_params:
            camera_location = camera_params.get('location', [0, 0, 1000])
            camera_height = camera_location[2]

        # 计算高度影响因子（高度越低，物体看起来越大）
        # 使用反比例关系，但限制在合理范围内
        height_scale_factor = max(0.5, min(2.0, 1000.0 / camera_height))
        logger.info(f"相机高度: {camera_height:.2f}米, 高度缩放因子: {height_scale_factor:.3f}")

        for obj in virtual_objects:
            position = obj['position']
            obj_rcs = obj['rcs']
            bbox = obj['bbox']

            # 计算距离衰减效应（雷达方程中的R^4衰减）
            obj_height = position[2]
            slant_range = np.sqrt((camera_height - obj_height)**2)  # 简化的斜距计算
            range_factor = (100.0 / max(slant_range, 10.0))**2  # 距离平方衰减，归一化到100米

            # 应用距离衰减到RCS
            effective_rcs = obj_rcs * range_factor
            
            # 转换到像素坐标
            pixel_x = int((position[0] - world_bounds['min_x']) / pixel_to_world_x)
            pixel_y = int((position[1] - world_bounds['min_y']) / pixel_to_world_y)
            
            # 计算对象在图像中的尺寸，考虑相机高度的影响
            obj_width = bbox['max_x'] - bbox['min_x']
            obj_height = bbox['max_y'] - bbox['min_y']

            # 应用高度缩放因子
            effective_obj_width = obj_width * height_scale_factor
            effective_obj_height = obj_height * height_scale_factor

            pixel_width = max(1, int(effective_obj_width / pixel_to_world_x))
            pixel_height = max(1, int(effective_obj_height / pixel_to_world_y))
            
            # 在对象区域内分布RCS
            if obj['type'] == 'ground':
                # 地面使用稀疏的随机分布，避免均匀亮斑
                for dy in range(-pixel_height//2, pixel_height//2 + 1):
                    for dx in range(-pixel_width//2, pixel_width//2 + 1):
                        px = pixel_x + dx
                        py = pixel_y + dy
                        if 0 <= px < width and 0 <= py < height:
                            # 只有极少的像素有地面回波，且强度极低
                            if np.random.random() < 0.002:  # 只有0.2%的像素有地面回波
                                rcs_map[py, px] += effective_rcs * np.random.uniform(0.01, 0.05)
            else:
                # 飞机使用椭圆形高斯分布，避免矩形阴影
                # 计算椭圆参数
                a = pixel_width / 2  # 长轴
                b = pixel_height / 2  # 短轴

                # 使用更大的搜索范围以确保完整的椭圆形状
                search_range_x = int(a * 1.5)
                search_range_y = int(b * 1.5)

                for dy in range(-search_range_y, search_range_y + 1):
                    for dx in range(-search_range_x, search_range_x + 1):
                        px = pixel_x + dx
                        py = pixel_y + dy

                        if 0 <= px < width and 0 <= py < height:
                            # 计算椭圆内的归一化距离
                            ellipse_dist = (dx/max(a, 1))**2 + (dy/max(b, 1))**2

                            # 只在椭圆内部分布RCS
                            if ellipse_dist <= 1.0:
                                # 使用高斯分布，中心最强
                                gaussian_factor = np.exp(-ellipse_dist * 2)  # 控制衰减速度

                                # 添加一些随机性以模拟真实散射
                                random_factor = np.random.uniform(0.8, 1.2)

                                rcs_map[py, px] += effective_rcs * gaussian_factor * random_factor
        
        return rcs_map
    
    def _simulate_radar_echo(self, rcs_map: np.ndarray,
                           scene_info: Dict, camera_params: Dict) -> np.ndarray:
        """
        仿真雷达回波数据

        Args:
            rcs_map: RCS分布图
            scene_info: 场景信息
            camera_params: 相机参数

        Returns:
            原始回波数据
        """
        height, width = rcs_map.shape

        # 获取雷达参数
        radar_freq = 10e9  # 10 GHz X波段
        c = 3e8  # 光速
        wavelength = c / radar_freq

        # 计算距离信息
        scene_bounds = scene_info.get('scene_bounds', {
            'min_x': -500, 'max_x': 500,
            'min_y': -500, 'max_y': 500,
            'min_z': 0, 'max_z': 50
        })

        # 使用相机参数中的高度作为雷达高度
        camera_location = camera_params.get('location', [0, 0, 1000])
        radar_height = camera_location[2]  # 使用相机高度作为雷达高度
        logger.info(f"使用雷达高度: {radar_height:.2f}米")

        # 创建距离矩阵
        world_width = scene_bounds['max_x'] - scene_bounds['min_x']
        world_height = scene_bounds['max_y'] - scene_bounds['min_y']

        x_coords = np.linspace(scene_bounds['min_x'], scene_bounds['max_x'], width)
        y_coords = np.linspace(scene_bounds['min_y'], scene_bounds['max_y'], height)
        X, Y = np.meshgrid(x_coords, y_coords)

        # 计算到雷达的距离
        range_matrix = np.sqrt(X**2 + Y**2 + radar_height**2)

        # 计算雷达方程中的传播损耗
        # P_r = P_t * G^2 * λ^2 * σ / ((4π)^3 * R^4)
        # 这里简化为 1/R^2 的损耗
        propagation_loss = 1.0 / (range_matrix**2 + 1e-6)  # 避免除零

        # 应用传播损耗到RCS
        received_power = rcs_map * propagation_loss

        # 归一化到合理范围
        max_power = np.max(received_power)
        if max_power > 0:
            received_power = received_power / max_power

        # 生成复数回波信号
        # 幅度基于接收功率
        amplitude = np.sqrt(received_power)

        # 相位基于距离和随机散射特性
        range_phase = 4 * np.pi * range_matrix / wavelength
        random_phase = np.random.uniform(0, 2*np.pi, rcs_map.shape)
        total_phase = range_phase + random_phase

        # 构造复数回波
        raw_echo = amplitude * np.exp(1j * total_phase)

        # 添加相干斑点效应（多个散射体的相干叠加）
        speckle_real = np.random.normal(0, 0.1, rcs_map.shape)
        speckle_imag = np.random.normal(0, 0.1, rcs_map.shape)
        speckle = speckle_real + 1j * speckle_imag

        raw_echo += speckle * amplitude * 0.2  # 添加20%的相干斑点

        return raw_echo
    
    def _process_sar_imaging(self, raw_echo_data: np.ndarray) -> np.ndarray:
        """
        SAR成像处理

        Args:
            raw_echo_data: 原始回波数据

        Returns:
            SAR复数图像
        """
        # 实现更真实的SAR成像处理

        # 1. 距离压缩（Range Compression）
        # 应用匹配滤波器进行距离压缩
        range_compressed = self._range_compression(raw_echo_data)

        # 2. 距离单元迁移校正（Range Cell Migration Correction）
        rcmc_corrected = self._range_cell_migration_correction(range_compressed)

        # 3. 方位压缩（Azimuth Compression）
        azimuth_compressed = self._azimuth_compression(rcmc_corrected)

        # 4. 应用窗函数减少旁瓣
        windowed_data = self.rd_processor.apply_window_function(azimuth_compressed, 'hamming')

        return windowed_data

    def _range_compression(self, raw_data: np.ndarray) -> np.ndarray:
        """
        距离压缩处理

        Args:
            raw_data: 原始回波数据

        Returns:
            距离压缩后的数据
        """
        # 简化的距离压缩：使用高斯滤波器模拟匹配滤波
        from scipy import ndimage

        # 对每一行（距离方向）应用滤波
        compressed_data = np.zeros_like(raw_data, dtype=complex)

        for i in range(raw_data.shape[0]):
            # 实部和虚部分别处理
            real_filtered = ndimage.gaussian_filter1d(raw_data[i].real, sigma=1.0)
            imag_filtered = ndimage.gaussian_filter1d(raw_data[i].imag, sigma=1.0)
            compressed_data[i] = real_filtered + 1j * imag_filtered

        return compressed_data

    def _range_cell_migration_correction(self, range_compressed: np.ndarray) -> np.ndarray:
        """
        距离单元迁移校正

        Args:
            range_compressed: 距离压缩后的数据

        Returns:
            校正后的数据
        """
        # 简化的RCMC：这里直接返回输入数据
        # 实际应用中需要根据平台运动参数进行复杂的插值校正
        return range_compressed

    def _azimuth_compression(self, rcmc_data: np.ndarray) -> np.ndarray:
        """
        方位压缩处理

        Args:
            rcmc_data: RCMC校正后的数据

        Returns:
            方位压缩后的数据
        """
        # 简化的方位压缩：对每一列（方位方向）应用滤波
        from scipy import ndimage

        compressed_data = np.zeros_like(rcmc_data, dtype=complex)

        for j in range(rcmc_data.shape[1]):
            # 实部和虚部分别处理
            real_filtered = ndimage.gaussian_filter1d(rcmc_data[:, j].real, sigma=1.5)
            imag_filtered = ndimage.gaussian_filter1d(rcmc_data[:, j].imag, sigma=1.5)
            compressed_data[:, j] = real_filtered + 1j * imag_filtered

        return compressed_data
    
    def _post_process_image(self, sar_image: np.ndarray) -> np.ndarray:
        """
        图像后处理

        Args:
            sar_image: SAR复数图像

        Returns:
            处理后的图像
        """
        # 计算幅度图像
        magnitude_image = np.abs(sar_image)

        # 应用噪声抑制
        denoised_image = self._suppress_background_noise(magnitude_image)

        # 应用自适应对比度增强
        enhanced_image = self._adaptive_contrast_enhancement(denoised_image)

        # 对数压缩以增强动态范围
        log_image = 20 * np.log10(enhanced_image + 1e-12)

        # 使用更严格的百分位数进行归一化，确保背景暗化
        p2, p98 = np.percentile(log_image, [2, 98])  # 使用更严格的范围

        # 限制动态范围以避免极值影响，并确保背景暗化
        clipped_image = np.clip(log_image, p2, p98)

        # 归一化到0-255，但压缩低值范围
        if p98 > p2:
            # 对低值区域进行非线性压缩
            normalized_linear = (clipped_image - p2) / (p98 - p2)
            # 应用幂函数压缩低值，突出高值
            normalized_compressed = np.power(normalized_linear, 1.5) * 255
            normalized = normalized_compressed
        else:
            normalized = np.zeros_like(clipped_image)

        # 转换为uint8
        final_image = normalized.astype(np.uint8)

        return final_image

    def _suppress_background_noise(self, image: np.ndarray) -> np.ndarray:
        """
        抑制背景噪声

        Args:
            image: 输入图像

        Returns:
            降噪后的图像
        """
        # 使用更严格的噪声阈值
        noise_threshold = np.percentile(image, 90)  # 90%分位数作为噪声阈值

        # 对低于阈值的像素进行极强抑制
        suppressed_image = image.copy()

        # 极低信号区域（几乎置零）
        very_low_signal_mask = image < noise_threshold * 0.05
        suppressed_image[very_low_signal_mask] *= 0.001  # 几乎完全抑制

        # 低信号区域（强烈抑制）
        low_signal_mask = (image >= noise_threshold * 0.05) & (image < noise_threshold * 0.15)
        suppressed_image[low_signal_mask] *= 0.01  # 强烈抑制背景噪声

        # 中等信号进行适度抑制
        medium_signal_mask = (image >= noise_threshold * 0.15) & (image < noise_threshold * 0.4)
        suppressed_image[medium_signal_mask] *= 0.2

        return suppressed_image

    def _adaptive_contrast_enhancement(self, image: np.ndarray) -> np.ndarray:
        """
        自适应对比度增强

        Args:
            image: 输入图像

        Returns:
            增强后的图像
        """
        # 计算局部统计
        from scipy import ndimage

        # 局部均值
        local_mean = ndimage.uniform_filter(image, size=15)

        # 局部标准差
        local_var = ndimage.uniform_filter(image**2, size=15) - local_mean**2
        local_std = np.sqrt(np.maximum(local_var, 0))

        # 自适应增强
        enhanced = np.where(local_std > 0,
                          (image - local_mean) / (local_std + 1e-10) * 0.5 + local_mean,
                          image)

        # 确保非负值
        enhanced = np.maximum(enhanced, 0)

        return enhanced
    
    def _collect_imaging_info(self, scene_info: Dict, camera_params: Dict,
                            sar_complex: np.ndarray, final_image: np.ndarray) -> Dict:
        """
        收集成像信息
        
        Args:
            scene_info: 场景信息
            camera_params: 相机参数
            sar_complex: SAR复数图像
            final_image: 最终图像
            
        Returns:
            成像信息字典
        """
        # 计算图像质量指标
        quality_metrics = self.rd_processor.calculate_image_quality_metrics(sar_complex)
        
        # 计算噪声统计
        if final_image is not None:
            noise_stats = self.noise_generator.calculate_noise_statistics(
                final_image.astype(float)
            )
            image_size = final_image.shape
        else:
            noise_stats = {}
            image_size = self.image_size

        imaging_info = {
            'scene_id': scene_info.get('scene_id'),
            'image_size': image_size,
            'radar_parameters': self.radar_simulator.get_radar_parameters(),
            'camera_parameters': camera_params,
            'quality_metrics': quality_metrics,
            'noise_statistics': noise_stats,
            'aircraft_count': scene_info.get('aircraft_count', 0),
            'aircraft_types': scene_info.get('aircraft_types', [])
        }
        
        return imaging_info
