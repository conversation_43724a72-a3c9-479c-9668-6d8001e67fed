"""
Range-Doppler (RD) 处理器
"""
import numpy as np
import scipy.signal
from scipy.fft import fft, ifft, fft2, ifft2, fftshift, ifftshift
from typing import Tuple, Dict, Optional
import logging

logger = logging.getLogger(__name__)


class RDProcessor:
    """Range-Doppler处理器，实现SAR成像的核心算法"""
    
    def __init__(self, radar_params: Dict):
        """
        初始化RD处理器
        
        Args:
            radar_params: 雷达参数字典
        """
        self.frequency = radar_params['frequency']
        self.wavelength = radar_params['wavelength']
        self.bandwidth = radar_params['bandwidth']
        self.prf = radar_params['prf']
        self.c = radar_params['c']
        
        # 计算处理参数
        self.range_resolution = self.c / (2 * self.bandwidth)
        
    def range_compression(self, raw_data: np.ndarray, 
                         reference_signal: np.ndarray) -> np.ndarray:
        """
        距离压缩处理
        
        Args:
            raw_data: 原始回波数据 (range_bins x azimuth_bins)
            reference_signal: 参考信号（chirp信号）
            
        Returns:
            距离压缩后的数据
        """
        # 对参考信号进行共轭
        ref_conj = np.conj(reference_signal)
        
        # 对每个方位单元进行距离压缩
        compressed_data = np.zeros_like(raw_data, dtype=complex)
        
        for az_idx in range(raw_data.shape[1]):
            # 取出一个方位单元的距离数据
            range_line = raw_data[:, az_idx]
            
            # 频域相关（匹配滤波）
            range_fft = fft(range_line)
            ref_fft = fft(ref_conj, n=len(range_line))
            
            # 匹配滤波
            compressed_fft = range_fft * ref_fft
            
            # 逆FFT得到压缩结果
            compressed_data[:, az_idx] = ifft(compressed_fft)
        
        logger.debug("距离压缩完成")
        return compressed_data
    
    def azimuth_compression(self, range_compressed_data: np.ndarray,
                           platform_velocity: float,
                           range_to_target: float) -> np.ndarray:
        """
        方位压缩处理
        
        Args:
            range_compressed_data: 距离压缩后的数据
            platform_velocity: 平台速度 (m/s)
            range_to_target: 到目标的距离 (m)
            
        Returns:
            方位压缩后的数据
        """
        # 计算方位向参数
        azimuth_bins = range_compressed_data.shape[1]
        
        # 生成方位向参考函数
        azimuth_ref = self._generate_azimuth_reference(
            azimuth_bins, platform_velocity, range_to_target
        )
        
        # 对每个距离单元进行方位压缩
        compressed_data = np.zeros_like(range_compressed_data, dtype=complex)
        
        for range_idx in range(range_compressed_data.shape[0]):
            # 取出一个距离单元的方位数据
            azimuth_line = range_compressed_data[range_idx, :]
            
            # 频域相关
            azimuth_fft = fft(azimuth_line)
            ref_fft = fft(np.conj(azimuth_ref))
            
            # 匹配滤波
            compressed_fft = azimuth_fft * ref_fft
            
            # 逆FFT得到压缩结果
            compressed_data[range_idx, :] = ifft(compressed_fft)
        
        logger.debug("方位压缩完成")
        return compressed_data
    
    def _generate_azimuth_reference(self, azimuth_bins: int,
                                   platform_velocity: float,
                                   range_to_target: float) -> np.ndarray:
        """
        生成方位向参考函数
        
        Args:
            azimuth_bins: 方位单元数
            platform_velocity: 平台速度
            range_to_target: 到目标距离
            
        Returns:
            方位向参考函数
        """
        # 方位时间轴
        azimuth_time = np.arange(azimuth_bins) / self.prf
        azimuth_time = azimuth_time - azimuth_time[azimuth_bins//2]  # 中心化
        
        # 计算瞬时距离
        instantaneous_range = np.sqrt(
            range_to_target**2 + (platform_velocity * azimuth_time)**2
        )
        
        # 计算相位历程
        phase_history = 4 * np.pi * instantaneous_range / self.wavelength
        
        # 生成参考函数
        reference = np.exp(1j * phase_history)
        
        return reference
    
    def range_cell_migration_correction(self, data: np.ndarray,
                                       platform_velocity: float) -> np.ndarray:
        """
        距离徙动校正
        
        Args:
            data: 输入数据
            platform_velocity: 平台速度
            
        Returns:
            校正后的数据
        """
        range_bins, azimuth_bins = data.shape
        corrected_data = np.zeros_like(data, dtype=complex)
        
        # 计算距离徙动量
        for az_idx in range(azimuth_bins):
            azimuth_time = (az_idx - azimuth_bins//2) / self.prf
            
            for range_idx in range(range_bins):
                # 计算当前距离
                current_range = range_idx * self.range_resolution
                
                # 计算徙动量
                migration = (platform_velocity * azimuth_time)**2 / (2 * current_range)
                migration_bins = migration / self.range_resolution
                
                # 插值校正
                source_range_idx = range_idx - migration_bins
                
                if 0 <= source_range_idx < range_bins - 1:
                    # 线性插值
                    idx_low = int(source_range_idx)
                    idx_high = idx_low + 1
                    weight = source_range_idx - idx_low
                    
                    corrected_data[range_idx, az_idx] = (
                        (1 - weight) * data[idx_low, az_idx] +
                        weight * data[idx_high, az_idx]
                    )
        
        logger.debug("距离徙动校正完成")
        return corrected_data
    
    def secondary_range_compression(self, data: np.ndarray) -> np.ndarray:
        """
        二次距离压缩
        
        Args:
            data: 输入数据
            
        Returns:
            二次压缩后的数据
        """
        # 这里实现简化的二次距离压缩
        # 实际实现需要考虑距离向的二次相位项
        
        range_bins, azimuth_bins = data.shape
        compressed_data = np.zeros_like(data, dtype=complex)
        
        # 对每个方位单元进行处理
        for az_idx in range(azimuth_bins):
            range_line = data[:, az_idx]
            
            # 应用二次相位校正
            range_axis = np.arange(range_bins) * self.range_resolution
            quadratic_phase = np.exp(1j * np.pi * range_axis**2 / 
                                   (self.c * self.range_resolution))
            
            corrected_line = range_line * quadratic_phase
            compressed_data[:, az_idx] = corrected_line
        
        logger.debug("二次距离压缩完成")
        return compressed_data
    
    def process_rd_algorithm(self, raw_data: np.ndarray,
                            reference_signal: np.ndarray,
                            platform_velocity: float,
                            reference_range: float) -> np.ndarray:
        """
        完整的RD算法处理流程
        
        Args:
            raw_data: 原始回波数据
            reference_signal: 参考信号
            platform_velocity: 平台速度
            reference_range: 参考距离
            
        Returns:
            SAR图像
        """
        logger.info("开始RD算法处理")
        
        # 1. 距离压缩
        range_compressed = self.range_compression(raw_data, reference_signal)
        
        # 2. 距离徙动校正
        rcmc_data = self.range_cell_migration_correction(
            range_compressed, platform_velocity
        )
        
        # 3. 方位压缩
        azimuth_compressed = self.azimuth_compression(
            rcmc_data, platform_velocity, reference_range
        )
        
        # 4. 二次距离压缩（可选）
        final_image = self.secondary_range_compression(azimuth_compressed)
        
        logger.info("RD算法处理完成")
        return final_image
    
    def apply_window_function(self, data: np.ndarray, 
                             window_type: str = 'hamming') -> np.ndarray:
        """
        应用窗函数以减少旁瓣
        
        Args:
            data: 输入数据
            window_type: 窗函数类型
            
        Returns:
            加窗后的数据
        """
        range_bins, azimuth_bins = data.shape
        
        # 生成窗函数
        if window_type == 'hamming':
            range_window = np.hamming(range_bins)
            azimuth_window = np.hamming(azimuth_bins)
        elif window_type == 'hanning':
            range_window = np.hanning(range_bins)
            azimuth_window = np.hanning(azimuth_bins)
        elif window_type == 'blackman':
            range_window = np.blackman(range_bins)
            azimuth_window = np.blackman(azimuth_bins)
        else:
            # 矩形窗（无加窗）
            range_window = np.ones(range_bins)
            azimuth_window = np.ones(azimuth_bins)
        
        # 创建二维窗函数
        window_2d = np.outer(range_window, azimuth_window)
        
        # 应用窗函数
        windowed_data = data * window_2d
        
        logger.debug(f"应用{window_type}窗函数")
        return windowed_data
    
    def calculate_image_quality_metrics(self, sar_image: np.ndarray) -> Dict:
        """
        计算SAR图像质量指标
        
        Args:
            sar_image: SAR图像
            
        Returns:
            质量指标字典
        """
        # 计算图像幅度
        magnitude = np.abs(sar_image)
        
        # 信噪比估计
        signal_power = np.mean(magnitude**2)
        noise_power = np.var(magnitude)
        snr_db = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else np.inf
        
        # 动态范围
        max_value = np.max(magnitude)
        min_value = np.min(magnitude[magnitude > 0]) if np.any(magnitude > 0) else 1e-10
        dynamic_range_db = 20 * np.log10(max_value / min_value)
        
        # 图像熵
        hist, _ = np.histogram(magnitude, bins=256, density=True)
        hist = hist[hist > 0]  # 移除零值
        entropy = -np.sum(hist * np.log2(hist))
        
        metrics = {
            'snr_db': snr_db,
            'dynamic_range_db': dynamic_range_db,
            'entropy': entropy,
            'mean_magnitude': np.mean(magnitude),
            'std_magnitude': np.std(magnitude),
            'max_magnitude': max_value,
            'min_magnitude': min_value
        }
        
        return metrics
