"""
Blender运行器 - 用于启动Blender并执行Python脚本
"""
import subprocess
import os
import sys
import tempfile
import logging
from typing import Optional, Dict, Any
from .config_loader import config

logger = logging.getLogger(__name__)


class BlenderRunner:
    """Blender运行器"""
    
    def __init__(self):
        """初始化Blender运行器"""
        self.blender_path = config.get('blender.executable_path')
        self.headless = config.get('blender.headless', True)
        
    def is_blender_available(self) -> bool:
        """
        检查Blender是否可用
        
        Returns:
            Blender是否可用
        """
        if not self.blender_path:
            return False
            
        return os.path.exists(self.blender_path)
    
    def run_script(self, script_content: str, script_args: Optional[Dict[str, Any]] = None) -> bool:
        """
        在Blender中运行Python脚本
        
        Args:
            script_content: Python脚本内容
            script_args: 传递给脚本的参数
            
        Returns:
            是否成功执行
        """
        if not self.is_blender_available():
            logger.error(f"Blender不可用: {self.blender_path}")
            return False
            
        try:
            # 创建临时脚本文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
                # 添加参数传递代码
                if script_args:
                    f.write("# 脚本参数\n")
                    f.write(f"SCRIPT_ARGS = {repr(script_args)}\n\n")
                
                # 添加主脚本内容
                f.write(script_content)
                script_path = f.name
            
            # 构建Blender命令
            cmd = [self.blender_path]
            
            if self.headless:
                cmd.extend(['--background'])
            
            # 添加Python脚本执行参数
            cmd.extend(['--python', script_path])
            
            logger.info(f"启动Blender: {' '.join(cmd)}")
            
            # 执行Blender
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                cwd=os.getcwd(),
                timeout=600  # 10分钟超时
            )
            
            # 清理临时文件
            try:
                os.unlink(script_path)
            except:
                pass
            
            # 保存完整输出到文件
            if result.stdout:
                output_file = "blender_output.log"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(result.stdout)
                logger.info(f"Blender完整输出已保存到: {output_file}")

                # 显示前1000个字符
                preview = result.stdout[:1000]
                if len(result.stdout) > 1000:
                    preview += "...(输出被截断，完整内容见blender_output.log)"
                logger.info(f"Blender输出预览: {preview}")

            # 检查执行结果
            if result.returncode == 0:
                # 进一步检查输出内容确认成功
                if result.stdout and "SUCCESS:" in result.stdout:
                    logger.info("Blender脚本执行成功")
                    return True
                elif result.stdout and "ERROR:" in result.stdout:
                    logger.error("Blender脚本内部执行失败")
                    return False
                else:
                    logger.info("Blender脚本执行成功（无明确成功标识）")
                    return True
            else:
                logger.error(f"Blender脚本执行失败，返回码: {result.returncode}")
                if result.stderr:
                    logger.error(f"错误信息: {result.stderr}")
                if result.stdout:
                    logger.error(f"输出信息: {result.stdout}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Blender执行超时")
            return False
        except Exception as e:
            logger.error(f"执行Blender脚本时发生错误: {e}")
            return False
    
    def run_dataset_generation(self, num_images: int = 2, config_overrides: Optional[Dict] = None) -> bool:
        """
        运行数据集生成

        Args:
            num_images: 要生成的图像数量
            config_overrides: 配置覆盖字典

        Returns:
            是否成功
        """
        script_content = f'''
import sys
import os

# 添加项目路径到Python路径
project_root = r"{os.getcwd()}"
src_path = os.path.join(project_root, "src")
if src_path not in sys.path:
    sys.path.insert(0, src_path)

try:
    # 导入必要的模块
    from batch import BatchGenerator
    from utils import config, setup_logger

    # 设置日志
    logger = setup_logger('blender_generation')
    logger.info("在Blender中开始数据集生成...")

    # 临时修改配置 - 在创建BatchGenerator之前
    original_config = dict()

    # 保存原始配置并应用覆盖
    original_config['total_images'] = config.get('dataset.total_images')
    config._config['dataset']['total_images'] = {num_images}
    logger.info("配置修改: total_images 从 " + str(original_config['total_images']) + " 改为 " + str({num_images}))

    # 应用其他配置覆盖
    # 暂时跳过配置覆盖，避免复杂的字符串处理
    # TODO: 实现更好的配置覆盖机制
    config_overrides = dict()
    for key, value in config_overrides.items():
        if '.' in key:
            # 处理嵌套键，如 'dataset.output_dir'
            parts = key.split('.')
            current = config._config
            for part in parts[:-1]:
                if part not in current:
                    current[part] = dict()
                current = current[part]
            original_config[key] = current.get(parts[-1])
            current[parts[-1]] = value
            logger.info("配置覆盖: " + str(key) + " = " + str(value))
        else:
            original_config[key] = config.get(key)
            config._config[key] = value
            logger.info("配置覆盖: " + str(key) + " = " + str(value))

    # 创建批量生成器
    batch_generator = BatchGenerator()
    logger.info("BatchGenerator创建完成，total_images = " + str(batch_generator.total_images))

    logger.info(f"开始生成 {num_images} 张图像...")

    # 生成数据集
    success = batch_generator.generate_dataset()

    if success:
        logger.info("数据集生成成功！")

        # 获取生成统计
        stats = batch_generator.get_generation_status()
        logger.info("生成统计: " + str(stats))

        # 恢复原始配置
        config._config['dataset']['total_images'] = original_config['total_images']

        # 恢复其他配置覆盖
        for key, value in original_config.items():
            if key == 'total_images':
                continue  # 已经处理过了
            if '.' in key:
                parts = key.split('.')
                current = config._config
                for part in parts[:-1]:
                    current = current[part]
                current[parts[-1]] = value
            else:
                config._config[key] = value

        print("SUCCESS: Dataset generation completed")
    else:
        logger.error("数据集生成失败")
        print("ERROR: Dataset generation failed")
        import sys
        sys.exit(1)

except Exception as e:
    print("ERROR: " + str(e))
    import traceback
    traceback.print_exc()
    import sys
    sys.exit(1)
'''

        return self.run_script(script_content)


# 全局实例
blender_runner = BlenderRunner()
