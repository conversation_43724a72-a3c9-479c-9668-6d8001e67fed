# SAR图像仿真数据集生成配置文件

# Blender配置
blender:
  executable_path: "D:/Softwares/Blender Foundation/Blender 4.5/blender.exe"
  headless: true  # 无头模式运行
  
# 数据集配置
dataset:
  output_dir: "./output"
  image_size: [512, 512]  # 固定图像大小
  total_images: 10
  train_ratio: 0.7
  val_ratio: 0.2
  test_ratio: 0.1
  
# 飞机模型配置
aircraft:
  models_dir: "./assets/jets"
  # 飞机型号列表
  models:
    - name: "Boeing737"
      file: "Boeing737.fbx"
      category_id: 1
      length: 39.5  # 米，用于标准化
    - name: "Boeing747"
      file: "Boeing747.fbx"
      category_id: 2
      length: 70.6
    - name: "F16"
      file: "F16.FBX"
      category_id: 3
      length: 15.06
    - name: "F22"
      file: "F22.fbx"
      category_id: 4
      length: 18.9
    - name: "RQ180"
      file: "RQ180.fbx"
      category_id: 5
      length: 40.0  # 估计值
    - name: "SR71"
      file: "SR71.fbx"
      category_id: 6
      length: 32.74
    - name: "X35"
      file: "X35.FBX"
      category_id: 7
      length: 15.37
  
  # 标准化长度（所有飞机将缩放到此长度）
  standard_length: 20.0  # 米
  
# 场景配置
scene:
  ground_size: [1000, 1000]  # 地面大小（米）
  aircraft_count:
    min: 3
    max: 15
  
  # 飞机放置约束
  placement:
    height_range: [0, 50]  # 高度范围（米）
    pitch_range: [-15, 15]  # 俯仰角范围（度）
    yaw_range: [0, 360]  # 偏航角范围（度）
    roll_range: [-10, 10]  # 滚转角范围（度）
    min_distance: 30  # 飞机间最小距离（米）
    
# 相机配置
camera:
  type: "orthographic"  # 正交投影，适合俯视SAR成像
  height: 100  # 相机高度（米）
  angle: 0  # 俯视角度（度，0为垂直向下）
  
# SAR成像参数
sar:
  # 雷达参数
  frequency: 9.6e9  # X波段频率（Hz）
  bandwidth: 600e6  # 带宽（Hz）
  pulse_duration: 1e-6  # 脉冲持续时间（秒）
  prf: 1000  # 脉冲重复频率（Hz）
  
  # 成像参数
  range_resolution: 0.25  # 距离分辨率（米）
  azimuth_resolution: 0.25  # 方位分辨率（米）
  
  # 噪声参数
  noise:
    thermal_noise_power: -80  # 热噪声功率（dBm）
    clutter_ratio: 0.1  # 杂波比
    speckle_variance: 0.5  # 斑点噪声方差
    
# 材质配置
materials:
  # 金属材质（飞机）
  metal:
    conductivity: 5.8e7  # 电导率（S/m）
    permittivity: 1.0  # 相对介电常数
    roughness: 0.1  # 表面粗糙度
    
  # 地面材质
  ground:
    conductivity: 0.01  # 电导率（S/m）
    permittivity: 3.0  # 相对介电常数
    roughness: 0.8  # 表面粗糙度
    
# 输出配置
output:
  save_blender_files: false  # 是否保存.blend文件
  save_render_images: true  # 是否保存渲染图像
  image_format: "PNG"
  compression: 0.9
