"""
相机控制器
"""
import math
from typing import Tuple, Dict, Optional
import logging

try:
    import bpy
    from mathutils import Vector, Euler
    BLENDER_AVAILABLE = True
except ImportError:
    BLENDER_AVAILABLE = False
    class MockCamera:
        def __init__(self, name):
            self.name = name
            self.type = 'ORTHO'
            self.ortho_scale = 100
            self.lens = 50
            self.sensor_width = 36

    class MockObject:
        def __init__(self, name, data):
            self.name = name
            self.data = data
            self.location = (0, 0, 0)
            self.rotation_euler = (0, 0, 0)
    bpy = None

try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config
from .view_calculator import ViewCalculator

logger = logging.getLogger(__name__)


class CameraController:
    """相机控制器，负责设置和控制SAR成像相机"""
    
    def __init__(self):
        """初始化相机控制器"""
        self.view_calculator = ViewCalculator()
        
        # 从配置获取相机参数
        self.camera_config = config.get('camera', {})
        self.camera_type = self.camera_config.get('type', 'orthographic')
        self.default_height = self.camera_config.get('height', 2000)
        self.default_angle = self.camera_config.get('angle', 0)

        self.image_size = config.get('dataset.image_size', [512, 512])

        logger.info(f"相机控制器初始化: 高度={self.default_height}米, 类型={self.camera_type}")
        
        self.camera_obj = None
        self.camera_data = None
        
    def create_sar_camera(self, name: str = "SAR_Camera"):
        """
        创建SAR成像相机

        Args:
            name: 相机名称

        Returns:
            相机对象
        """
        try:
            if not BLENDER_AVAILABLE:
                # 在测试环境中返回模拟相机
                camera_data = MockCamera(name)
                camera_obj = MockObject(name, camera_data)
                self.camera_obj = camera_obj
                self.camera_data = camera_data
                return camera_obj

            # 删除现有相机
            if name in bpy.data.objects:
                bpy.data.objects.remove(bpy.data.objects[name], do_unlink=True)
                
            # 创建相机数据
            camera_data = bpy.data.cameras.new(name)
            camera_obj = bpy.data.objects.new(name, camera_data)
            
            # 添加到场景
            bpy.context.collection.objects.link(camera_obj)
            
            # 设置相机类型
            if self.camera_type == 'orthographic':
                camera_data.type = 'ORTHO'
                camera_data.ortho_scale = 100  # 默认值，后续会调整
            else:
                camera_data.type = 'PERSP'
                camera_data.lens = 50  # 默认焦距
                
            # 设置默认位置和旋转
            camera_obj.location = (0, 0, self.default_height)
            camera_obj.rotation_euler = (0, 0, 0)  # 垂直向下
            
            # 缓存相机对象
            self.camera_obj = camera_obj
            self.camera_data = camera_data
            
            logger.info(f"创建SAR相机: {name}, 类型: {self.camera_type}")
            return camera_obj
            
        except Exception as e:
            logger.error(f"创建相机失败: {e}")
            return None
    
    def setup_camera_for_scene(self, scene_bounds: Dict) -> bool:
        """
        根据场景边界设置相机参数
        
        Args:
            scene_bounds: 场景边界字典
            
        Returns:
            是否成功
        """
        if not self.camera_obj or not self.camera_data:
            logger.error("相机未创建")
            return False
            
        try:
            # 计算相机位置，使用配置的高度
            camera_pos = self.view_calculator.calculate_camera_position(scene_bounds, height=self.default_height)
            self.camera_obj.location = camera_pos

            logger.info(f"设置相机位置: {camera_pos}, 配置高度: {self.default_height}米")
            
            # 设置俯视角度
            self.camera_obj.rotation_euler = (0, 0, 0)  # 垂直向下
            
            if self.camera_type == 'orthographic':
                # 计算正交相机尺寸
                ortho_size = self.view_calculator.calculate_orthographic_size(scene_bounds)
                self.camera_data.ortho_scale = ortho_size * 2  # Blender的ortho_scale是直径
                
                # 验证覆盖范围
                if not self.view_calculator.validate_coverage(camera_pos, ortho_size, scene_bounds):
                    logger.warning("相机可能无法完全覆盖场景")
                    
            else:
                # 透视相机设置
                camera_height = camera_pos[2]
                fov = self.view_calculator.calculate_perspective_fov(scene_bounds, camera_height)
                
                # 将FOV转换为焦距
                sensor_width = 36  # 默认传感器宽度（mm）
                focal_length = sensor_width / (2 * math.tan(fov / 2))
                self.camera_data.lens = focal_length
                
            logger.info(f"相机设置完成 - 位置: {camera_pos}, 类型: {self.camera_type}")
            return True
            
        except Exception as e:
            logger.error(f"设置相机失败: {e}")
            return False
    
    def set_as_active_camera(self) -> bool:
        """
        将当前相机设置为活动相机
        
        Returns:
            是否成功
        """
        if not self.camera_obj:
            logger.error("相机未创建")
            return False
            
        try:
            bpy.context.scene.camera = self.camera_obj
            logger.info(f"设置活动相机: {self.camera_obj.name}")
            return True
        except Exception as e:
            logger.error(f"设置活动相机失败: {e}")
            return False
    
    def get_camera_parameters(self) -> Dict:
        """
        获取当前相机参数
        
        Returns:
            相机参数字典
        """
        if not self.camera_obj or not self.camera_data:
            return {}
            
        params = {
            'name': self.camera_obj.name,
            'type': self.camera_data.type,
            'location': list(self.camera_obj.location),
            'rotation': list(self.camera_obj.rotation_euler),
            'image_size': self.image_size
        }
        
        if self.camera_data.type == 'ORTHO':
            params['ortho_scale'] = self.camera_data.ortho_scale
            params['orthographic_size'] = self.camera_data.ortho_scale / 2
        else:
            params['lens'] = self.camera_data.lens
            params['sensor_width'] = self.camera_data.sensor_width
            
        return params
    
    def calculate_world_to_pixel_transform(self, scene_bounds: Dict) -> Dict:
        """
        计算世界坐标到像素坐标的变换参数
        
        Args:
            scene_bounds: 场景边界
            
        Returns:
            变换参数字典
        """
        if not self.camera_obj or self.camera_data.type != 'ORTHO':
            logger.error("只支持正交相机的坐标变换")
            return {}
            
        camera_pos = tuple(self.camera_obj.location)
        orthographic_size = self.camera_data.ortho_scale / 2
        
        # 计算像素到世界坐标的缩放比例
        x_scale, y_scale = self.view_calculator.calculate_pixel_to_world_scale(
            orthographic_size, tuple(self.image_size)
        )
        
        return {
            'camera_position': camera_pos,
            'orthographic_size': orthographic_size,
            'image_size': tuple(self.image_size),
            'pixel_to_world_scale': (x_scale, y_scale),
            'world_bounds': {
                'min_x': camera_pos[0] - orthographic_size,
                'max_x': camera_pos[0] + orthographic_size,
                'min_y': camera_pos[1] - orthographic_size,
                'max_y': camera_pos[1] + orthographic_size
            }
        }
    
    def world_to_pixel(self, world_pos: Tuple[float, float]) -> Tuple[int, int]:
        """
        将世界坐标转换为像素坐标
        
        Args:
            world_pos: 世界坐标 (x, y)
            
        Returns:
            像素坐标 (pixel_x, pixel_y)
        """
        if not self.camera_obj or self.camera_data.type != 'ORTHO':
            logger.error("只支持正交相机的坐标转换")
            return (0, 0)
            
        camera_pos = tuple(self.camera_obj.location)
        orthographic_size = self.camera_data.ortho_scale / 2
        
        return self.view_calculator.world_to_pixel_coordinates(
            world_pos, camera_pos, orthographic_size, tuple(self.image_size)
        )
    
    def get_object_pixel_bbox(self, object_bounds: Dict) -> Tuple[int, int, int, int]:
        """
        获取对象在图像中的像素边界框
        
        Args:
            object_bounds: 对象边界字典
            
        Returns:
            像素边界框 (min_x, min_y, max_x, max_y)
        """
        if not self.camera_obj or self.camera_data.type != 'ORTHO':
            logger.error("只支持正交相机的边界框计算")
            return (0, 0, 0, 0)
            
        camera_pos = tuple(self.camera_obj.location)
        orthographic_size = self.camera_data.ortho_scale / 2
        
        return self.view_calculator.calculate_object_pixel_bbox(
            object_bounds, camera_pos, orthographic_size, tuple(self.image_size)
        )
    
    def setup_render_settings(self) -> bool:
        """
        设置渲染参数
        
        Returns:
            是否成功
        """
        try:
            scene = bpy.context.scene
            
            # 设置渲染尺寸
            scene.render.resolution_x = self.image_size[0]
            scene.render.resolution_y = self.image_size[1]
            scene.render.resolution_percentage = 100
            
            # 设置渲染引擎
            scene.render.engine = 'CYCLES'  # 使用Cycles引擎以获得更好的材质效果
            
            # 设置采样
            scene.cycles.samples = 128  # 适中的采样数，平衡质量和速度
            
            # 设置输出格式
            scene.render.image_settings.file_format = 'PNG'
            scene.render.image_settings.color_mode = 'RGB'
            scene.render.image_settings.color_depth = '8'
            
            # 禁用不必要的效果
            scene.render.use_motion_blur = False
            scene.render.use_border = False
            
            logger.info(f"渲染设置完成 - 分辨率: {self.image_size}, 引擎: Cycles")
            return True
            
        except Exception as e:
            logger.error(f"设置渲染参数失败: {e}")
            return False
    
    def render_scene(self, output_path: str) -> bool:
        """
        渲染当前场景
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否成功
        """
        if not self.camera_obj:
            logger.error("相机未创建")
            return False
            
        try:
            # 设置输出路径
            bpy.context.scene.render.filepath = output_path
            
            # 确保相机是活动的
            self.set_as_active_camera()
            
            # 渲染
            bpy.ops.render.render(write_still=True)
            
            logger.info(f"场景渲染完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"渲染场景失败: {e}")
            return False
    
    def cleanup(self):
        """清理相机资源"""
        if self.camera_obj and self.camera_obj.name in bpy.data.objects:
            bpy.data.objects.remove(self.camera_obj, do_unlink=True)
            
        self.camera_obj = None
        self.camera_data = None
        logger.info("相机资源已清理")
