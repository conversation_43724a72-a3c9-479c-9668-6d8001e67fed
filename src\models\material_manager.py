"""
材质管理器
"""
from typing import Dict, Optional
import logging

try:
    import bpy
    BLENDER_AVAILABLE = True
except ImportError:
    BLENDER_AVAILABLE = False
    class MockMaterial:
        def __init__(self, name):
            self.name = name
            self.use_nodes = True
            self.node_tree = MockNodeTree()
        def __setitem__(self, key, value):
            pass
        def __getitem__(self, key):
            return 1.0
        def get(self, key, default=None):
            return default

    class MockNodeTree:
        def __init__(self):
            self.nodes = MockNodes()
            self.links = MockLinks()

    class MockNodes:
        def clear(self):
            pass
        def new(self, type):
            return MockNode()

    class MockLinks:
        def new(self, output, input):
            pass

    class MockNode:
        def __init__(self):
            self.location = (0, 0)
            self.inputs = {'Base Color': MockInput(), 'Metallic': MockInput(),
                          'Roughness': MockInput(), 'Specular': MockInput(), 'IOR': MockInput()}
            self.outputs = {'BSDF': MockOutput()}

    class MockInput:
        def __init__(self):
            self.default_value = 0.5

    class MockOutput:
        pass

    bpy = None

try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config

logger = logging.getLogger(__name__)


class MaterialManager:
    """材质管理器，用于创建和管理SAR成像所需的材质"""
    
    def __init__(self):
        """初始化材质管理器"""
        self.materials = {}  # 缓存创建的材质
        self.metal_config = config.get('materials.metal', {})
        self.ground_config = config.get('materials.ground', {})
        
    def create_metal_material(self, name: str = "SAR_Metal"):
        """
        创建金属材质（用于飞机）

        Args:
            name: 材质名称

        Returns:
            创建的材质对象
        """
        if name in self.materials:
            return self.materials[name]

        if not BLENDER_AVAILABLE:
            material = MockMaterial(name)
            self.materials[name] = material
            return material

        # 创建新材质
        material = bpy.data.materials.new(name=name)
        material.use_nodes = True
        
        # 清除默认节点
        material.node_tree.nodes.clear()
        
        # 创建节点
        nodes = material.node_tree.nodes
        links = material.node_tree.links
        
        # 输出节点
        output_node = nodes.new(type='ShaderNodeOutputMaterial')
        output_node.location = (300, 0)
        
        # 主着色器节点
        principled_node = nodes.new(type='ShaderNodeBsdfPrincipled')
        principled_node.location = (0, 0)
        
        # 设置金属材质属性
        principled_node.inputs['Base Color'].default_value = (0.7, 0.7, 0.7, 1.0)  # 灰色
        principled_node.inputs['Metallic'].default_value = 1.0  # 完全金属
        principled_node.inputs['Roughness'].default_value = self.metal_config.get('roughness', 0.1)

        # 在Blender 4.0+中，Specular被替换为IOR
        try:
            principled_node.inputs['Specular'].default_value = 1.0
        except KeyError:
            # Blender 4.0+使用IOR
            if 'IOR' in principled_node.inputs:
                principled_node.inputs['IOR'].default_value = 1.5
        
        # 连接节点
        links.new(principled_node.outputs['BSDF'], output_node.inputs['Surface'])
        
        # 添加自定义属性用于SAR仿真
        material['sar_conductivity'] = self.metal_config.get('conductivity', 5.8e7)
        material['sar_permittivity'] = self.metal_config.get('permittivity', 1.0)
        material['sar_material_type'] = 'metal'
        
        # 缓存材质
        self.materials[name] = material
        
        logger.info(f"创建金属材质: {name}")
        return material
    
    def create_ground_material(self, name: str = "SAR_Ground"):
        """
        创建地面材质

        Args:
            name: 材质名称

        Returns:
            创建的材质对象
        """
        if name in self.materials:
            return self.materials[name]

        if not BLENDER_AVAILABLE:
            material = MockMaterial(name)
            self.materials[name] = material
            return material

        # 创建新材质
        material = bpy.data.materials.new(name=name)
        material.use_nodes = True
        
        # 清除默认节点
        material.node_tree.nodes.clear()
        
        # 创建节点
        nodes = material.node_tree.nodes
        links = material.node_tree.links
        
        # 输出节点
        output_node = nodes.new(type='ShaderNodeOutputMaterial')
        output_node.location = (300, 0)
        
        # 主着色器节点
        principled_node = nodes.new(type='ShaderNodeBsdfPrincipled')
        principled_node.location = (0, 0)
        
        # 设置地面材质属性
        principled_node.inputs['Base Color'].default_value = (0.3, 0.25, 0.2, 1.0)  # 土色
        principled_node.inputs['Metallic'].default_value = 0.0  # 非金属
        principled_node.inputs['Roughness'].default_value = self.ground_config.get('roughness', 0.8)

        # 在Blender 4.0+中，Specular被替换为IOR
        try:
            principled_node.inputs['Specular'].default_value = 0.1
        except KeyError:
            # Blender 4.0+使用IOR
            if 'IOR' in principled_node.inputs:
                principled_node.inputs['IOR'].default_value = 1.45
        
        # 连接节点
        links.new(principled_node.outputs['BSDF'], output_node.inputs['Surface'])
        
        # 添加自定义属性用于SAR仿真
        material['sar_conductivity'] = self.ground_config.get('conductivity', 0.001)  # 进一步降低地面导电性
        material['sar_permittivity'] = self.ground_config.get('permittivity', 2.0)  # 降低介电常数
        material['sar_material_type'] = 'ground'
        
        # 缓存材质
        self.materials[name] = material
        
        logger.info(f"创建地面材质: {name}")
        return material
    
    def apply_material_to_object(self, obj, material) -> bool:
        """
        将材质应用到对象

        Args:
            obj: 目标对象
            material: 要应用的材质

        Returns:
            是否成功
        """
        if not BLENDER_AVAILABLE:
            return True  # 在测试环境中总是成功

        if not obj or obj.type != 'MESH':
            logger.error("对象不是有效的网格对象")
            return False
            
        try:
            # 清除现有材质
            obj.data.materials.clear()
            
            # 添加新材质
            obj.data.materials.append(material)
            
            logger.debug(f"材质已应用到对象: {obj.name} -> {material.name}")
            return True
            
        except Exception as e:
            logger.error(f"应用材质失败: {e}")
            return False
    
    def setup_aircraft_material(self, obj) -> bool:
        """
        为飞机对象设置金属材质
        
        Args:
            obj: 飞机对象
            
        Returns:
            是否成功
        """
        material = self.create_metal_material(f"SAR_Metal_{obj.name}")
        return self.apply_material_to_object(obj, material)
    
    def setup_ground_material(self, obj) -> bool:
        """
        为地面对象设置地面材质
        
        Args:
            obj: 地面对象
            
        Returns:
            是否成功
        """
        material = self.create_ground_material("SAR_Ground")
        return self.apply_material_to_object(obj, material)
    
    def get_material_properties(self, material) -> Dict:
        """
        获取材质的SAR相关属性
        
        Args:
            material: 材质对象
            
        Returns:
            属性字典
        """
        if not material:
            return {}
            
        return {
            'conductivity': material.get('sar_conductivity', 0.0),
            'permittivity': material.get('sar_permittivity', 1.0),
            'material_type': material.get('sar_material_type', 'unknown'),
            'roughness': self._get_roughness_from_material(material)
        }
    
    def _get_roughness_from_material(self, material) -> float:
        """
        从材质节点获取粗糙度值
        
        Args:
            material: 材质对象
            
        Returns:
            粗糙度值
        """
        if not material.use_nodes:
            return 0.5
            
        # 查找Principled BSDF节点
        for node in material.node_tree.nodes:
            if node.type == 'BSDF_PRINCIPLED':
                return node.inputs['Roughness'].default_value
                
        return 0.5
    
    def clear_materials(self):
        """清除所有缓存的材质"""
        self.materials.clear()
        logger.info("材质缓存已清除")
    
    def create_custom_material(self, name: str, properties: Dict):
        """
        创建自定义材质

        Args:
            name: 材质名称
            properties: 材质属性字典

        Returns:
            创建的材质对象
        """
        if name in self.materials:
            return self.materials[name]

        if not BLENDER_AVAILABLE:
            material = MockMaterial(name)
            self.materials[name] = material
            return material

        # 创建新材质
        material = bpy.data.materials.new(name=name)
        material.use_nodes = True
        
        # 清除默认节点
        material.node_tree.nodes.clear()
        
        # 创建基本节点结构
        nodes = material.node_tree.nodes
        links = material.node_tree.links
        
        output_node = nodes.new(type='ShaderNodeOutputMaterial')
        output_node.location = (300, 0)
        
        principled_node = nodes.new(type='ShaderNodeBsdfPrincipled')
        principled_node.location = (0, 0)
        
        # 设置属性
        if 'base_color' in properties:
            principled_node.inputs['Base Color'].default_value = properties['base_color']
        if 'metallic' in properties:
            principled_node.inputs['Metallic'].default_value = properties['metallic']
        if 'roughness' in properties:
            principled_node.inputs['Roughness'].default_value = properties['roughness']
        if 'specular' in properties:
            try:
                principled_node.inputs['Specular'].default_value = properties['specular']
            except KeyError:
                # Blender 4.0+使用IOR
                if 'IOR' in principled_node.inputs:
                    # 将specular值转换为合理的IOR值
                    ior_value = 1.0 + properties['specular'] * 0.5
                    principled_node.inputs['IOR'].default_value = ior_value
            
        # 连接节点
        links.new(principled_node.outputs['BSDF'], output_node.inputs['Surface'])
        
        # 添加SAR属性
        if 'sar_conductivity' in properties:
            material['sar_conductivity'] = properties['sar_conductivity']
        if 'sar_permittivity' in properties:
            material['sar_permittivity'] = properties['sar_permittivity']
        if 'sar_material_type' in properties:
            material['sar_material_type'] = properties['sar_material_type']
            
        # 缓存材质
        self.materials[name] = material
        
        logger.info(f"创建自定义材质: {name}")
        return material
