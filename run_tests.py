#!/usr/bin/env python3
"""
测试运行脚本
"""
import unittest
import sys
import os
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils import setup_logger

def discover_and_run_tests(test_dir='tests', pattern='test_*.py', verbosity=2):
    """
    发现并运行测试
    
    Args:
        test_dir: 测试目录
        pattern: 测试文件模式
        verbosity: 详细程度
        
    Returns:
        测试结果
    """
    # 设置日志
    logger = setup_logger("test_runner")
    
    logger.info("开始运行测试...")
    
    # 发现测试
    loader = unittest.TestLoader()
    start_dir = test_dir
    suite = loader.discover(start_dir, pattern=pattern)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=verbosity)
    result = runner.run(suite)
    
    # 输出结果摘要
    logger.info(f"测试完成:")
    logger.info(f"  运行测试数: {result.testsRun}")
    logger.info(f"  失败数: {len(result.failures)}")
    logger.info(f"  错误数: {len(result.errors)}")
    logger.info(f"  跳过数: {len(result.skipped)}")
    
    if result.failures:
        logger.error("失败的测试:")
        for test, traceback in result.failures:
            logger.error(f"  {test}: {traceback}")
    
    if result.errors:
        logger.error("错误的测试:")
        for test, traceback in result.errors:
            logger.error(f"  {test}: {traceback}")
    
    return result

def run_specific_test(test_module, test_class=None, test_method=None, verbosity=2):
    """
    运行特定测试
    
    Args:
        test_module: 测试模块名
        test_class: 测试类名（可选）
        test_method: 测试方法名（可选）
        verbosity: 详细程度
    """
    logger = setup_logger("test_runner")
    
    try:
        # 导入测试模块
        module = __import__(f'tests.{test_module}', fromlist=[test_module])
        
        if test_class and test_method:
            # 运行特定方法
            suite = unittest.TestSuite()
            test_case = getattr(module, test_class)(test_method)
            suite.addTest(test_case)
        elif test_class:
            # 运行特定类
            suite = unittest.TestLoader().loadTestsFromTestCase(getattr(module, test_class))
        else:
            # 运行整个模块
            suite = unittest.TestLoader().loadTestsFromModule(module)
        
        runner = unittest.TextTestRunner(verbosity=verbosity)
        result = runner.run(suite)
        
        return result
        
    except Exception as e:
        logger.error(f"运行测试失败: {e}")
        return None

def run_performance_tests():
    """运行性能测试"""
    logger = setup_logger("performance_test")
    
    logger.info("开始性能测试...")
    
    # 这里可以添加性能测试代码
    # 例如：测试SAR成像算法的执行时间
    
    import time
    import numpy as np
    
    # 测试SAR算法性能
    try:
        from sar.radar_simulator import RadarSimulator
        from sar.rd_processor import RDProcessor
        
        radar = RadarSimulator()
        rd_processor = RDProcessor(radar.get_radar_parameters())
        
        # 测试数据
        test_data = np.random.complex128((512, 512))
        
        # 测试窗函数应用性能
        start_time = time.time()
        windowed_data = rd_processor.apply_window_function(test_data, 'hamming')
        window_time = time.time() - start_time
        
        logger.info(f"窗函数应用时间: {window_time:.4f}秒")
        
        # 测试图像质量指标计算性能
        start_time = time.time()
        metrics = rd_processor.calculate_image_quality_metrics(test_data)
        metrics_time = time.time() - start_time
        
        logger.info(f"质量指标计算时间: {metrics_time:.4f}秒")
        
    except ImportError as e:
        logger.warning(f"无法导入SAR模块进行性能测试: {e}")
    
    logger.info("性能测试完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SAR仿真项目测试运行器")
    parser.add_argument("--module", help="运行特定测试模块")
    parser.add_argument("--class", dest="test_class", help="运行特定测试类")
    parser.add_argument("--method", help="运行特定测试方法")
    parser.add_argument("--pattern", default="test_*.py", help="测试文件模式")
    parser.add_argument("--verbosity", type=int, default=2, help="详细程度 (0-2)")
    parser.add_argument("--performance", action="store_true", help="运行性能测试")
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    
    args = parser.parse_args()
    
    if args.performance:
        run_performance_tests()
        return
    
    if args.coverage:
        try:
            import coverage
            cov = coverage.Coverage()
            cov.start()
            
            # 运行测试
            if args.module:
                result = run_specific_test(args.module, args.test_class, args.method, args.verbosity)
            else:
                result = discover_and_run_tests(pattern=args.pattern, verbosity=args.verbosity)
            
            cov.stop()
            cov.save()
            
            # 生成覆盖率报告
            print("\n覆盖率报告:")
            cov.report()
            
            # 生成HTML报告
            cov.html_report(directory='htmlcov')
            print("HTML覆盖率报告已生成到 htmlcov/ 目录")
            
        except ImportError:
            print("需要安装coverage包来生成覆盖率报告: pip install coverage")
            sys.exit(1)
    else:
        # 运行常规测试
        if args.module:
            result = run_specific_test(args.module, args.test_class, args.method, args.verbosity)
        else:
            result = discover_and_run_tests(pattern=args.pattern, verbosity=args.verbosity)
        
        # 根据测试结果设置退出码
        if result and (result.failures or result.errors):
            sys.exit(1)
        else:
            sys.exit(0)

if __name__ == "__main__":
    main()
