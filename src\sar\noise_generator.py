"""
噪声生成器
"""
import numpy as np
import scipy.stats
from typing import Tuple, Dict, Optional
import logging

try:
    from ..utils import config
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config

logger = logging.getLogger(__name__)


class NoiseGenerator:
    """噪声生成器，用于添加各种类型的SAR图像噪声"""
    
    def __init__(self):
        """初始化噪声生成器"""
        # 从配置获取噪声参数
        noise_config = config.get('sar.noise', {})

        self.thermal_noise_power = float(noise_config.get('thermal_noise_power', -60))  # dBm，降低噪声功率
        self.clutter_ratio = float(noise_config.get('clutter_ratio', 0.05))  # 降低杂波比例
        self.speckle_variance = float(noise_config.get('speckle_variance', 0.2))  # 降低斑点噪声
        
    def add_thermal_noise(self, signal: np.ndarray, 
                         noise_power_db: Optional[float] = None) -> np.ndarray:
        """
        添加热噪声
        
        Args:
            signal: 输入信号
            noise_power_db: 噪声功率 (dB)，如果为None则使用配置值
            
        Returns:
            添加噪声后的信号
        """
        if noise_power_db is None:
            noise_power_db = self.thermal_noise_power
            
        # 计算信号功率
        signal_power = np.mean(np.abs(signal)**2)

        # 确保信号功率不为零
        if signal_power < 1e-12:
            signal_power = 1e-12

        # 将噪声功率从dB转换为线性值，并相对于信号功率调整
        noise_power_db_relative = noise_power_db + 10 * np.log10(signal_power)
        noise_power_linear = 10**(noise_power_db_relative / 10)

        # 限制噪声功率，确保不会完全淹没信号
        max_noise_power = signal_power * 0.1  # 噪声功率不超过信号功率的10%
        noise_power_linear = min(noise_power_linear, max_noise_power)

        # 生成复高斯白噪声
        noise_std = np.sqrt(noise_power_linear / 2)
        noise_real = np.random.normal(0, noise_std, signal.shape)
        noise_imag = np.random.normal(0, noise_std, signal.shape)
        thermal_noise = noise_real + 1j * noise_imag

        # 添加噪声
        noisy_signal = signal + thermal_noise

        # 计算实际信噪比
        actual_noise_power = np.mean(np.abs(thermal_noise)**2)
        snr_db = 10 * np.log10(signal_power / actual_noise_power) if actual_noise_power > 0 else np.inf
        logger.debug(f"添加热噪声，SNR: {snr_db:.2f} dB")
        
        return noisy_signal
    
    def add_speckle_noise(self, image: np.ndarray, 
                         variance: Optional[float] = None) -> np.ndarray:
        """
        添加斑点噪声（SAR图像特有的乘性噪声）
        
        Args:
            image: 输入图像
            variance: 斑点噪声方差，如果为None则使用配置值
            
        Returns:
            添加斑点噪声后的图像
        """
        if variance is None:
            variance = self.speckle_variance
            
        # 生成更温和的斑点噪声
        # 使用较小的方差以减少噪声影响
        effective_variance = min(variance, 0.1)  # 限制最大方差

        # 生成Gamma分布的斑点噪声
        shape = 1 / effective_variance  # 形状参数
        scale = effective_variance      # 尺度参数

        # 生成斑点噪声
        speckle = np.random.gamma(shape, scale, image.shape)

        # 归一化斑点噪声（均值为1）
        speckle_mean = np.mean(speckle)
        if speckle_mean > 0:
            speckle = speckle / speckle_mean

        # 限制斑点噪声的范围，避免极值
        speckle = np.clip(speckle, 0.5, 2.0)

        # 应用乘性噪声
        speckled_image = image * speckle
        
        logger.debug(f"添加斑点噪声，方差: {variance}")
        return speckled_image
    
    def add_clutter_noise(self, signal: np.ndarray, 
                         clutter_ratio: Optional[float] = None) -> np.ndarray:
        """
        添加杂波噪声
        
        Args:
            signal: 输入信号
            clutter_ratio: 杂波与信号的功率比，如果为None则使用配置值
            
        Returns:
            添加杂波后的信号
        """
        if clutter_ratio is None:
            clutter_ratio = self.clutter_ratio
            
        # 计算信号功率
        signal_power = np.mean(np.abs(signal)**2)
        
        # 计算杂波功率
        clutter_power = signal_power * clutter_ratio
        
        # 生成相关杂波（使用低通滤波的白噪声）
        white_noise_real = np.random.normal(0, 1, signal.shape)
        white_noise_imag = np.random.normal(0, 1, signal.shape)
        white_noise = white_noise_real + 1j * white_noise_imag
        
        # 应用低通滤波产生相关性
        from scipy import ndimage
        sigma = 2.0  # 滤波器标准差
        filtered_real = ndimage.gaussian_filter(white_noise.real, sigma)
        filtered_imag = ndimage.gaussian_filter(white_noise.imag, sigma)
        correlated_clutter = filtered_real + 1j * filtered_imag
        
        # 归一化并缩放到所需功率
        clutter_std = np.sqrt(clutter_power / 2)  # 复信号的标准差
        normalized_clutter = correlated_clutter / np.std(correlated_clutter) * clutter_std
        
        # 添加杂波
        cluttered_signal = signal + normalized_clutter
        
        logger.debug(f"添加杂波噪声，杂波比: {clutter_ratio}")
        return cluttered_signal
    
    def add_phase_noise(self, signal: np.ndarray, 
                       phase_std: float = 0.1) -> np.ndarray:
        """
        添加相位噪声
        
        Args:
            signal: 输入信号
            phase_std: 相位噪声标准差 (弧度)
            
        Returns:
            添加相位噪声后的信号
        """
        # 生成相位噪声
        phase_noise = np.random.normal(0, phase_std, signal.shape)
        
        # 应用相位噪声
        phase_corrupted = signal * np.exp(1j * phase_noise)
        
        logger.debug(f"添加相位噪声，标准差: {phase_std:.3f} rad")
        return phase_corrupted
    
    def add_quantization_noise(self, signal: np.ndarray, 
                              bits: int = 8) -> np.ndarray:
        """
        添加量化噪声
        
        Args:
            signal: 输入信号
            bits: 量化位数
            
        Returns:
            量化后的信号
        """
        # 计算量化级数
        levels = 2**bits
        
        # 归一化信号到[-1, 1]
        signal_max = np.max(np.abs(signal))
        if signal_max > 0:
            normalized_signal = signal / signal_max
        else:
            normalized_signal = signal
            
        # 量化
        quantized_real = np.round(normalized_signal.real * (levels//2 - 1)) / (levels//2 - 1)
        quantized_imag = np.round(normalized_signal.imag * (levels//2 - 1)) / (levels//2 - 1)
        quantized_signal = quantized_real + 1j * quantized_imag
        
        # 恢复原始幅度
        quantized_signal = quantized_signal * signal_max
        
        logger.debug(f"应用{bits}位量化")
        return quantized_signal
    
    def add_atmospheric_effects(self, signal: np.ndarray,
                               attenuation_db: float = 0.1,
                               phase_delay: float = 0.0) -> np.ndarray:
        """
        添加大气效应
        
        Args:
            signal: 输入信号
            attenuation_db: 大气衰减 (dB)
            phase_delay: 大气相位延迟 (弧度)
            
        Returns:
            受大气影响的信号
        """
        # 应用衰减
        attenuation_linear = 10**(-attenuation_db / 20)
        attenuated_signal = signal * attenuation_linear
        
        # 应用相位延迟
        phase_shifted_signal = attenuated_signal * np.exp(1j * phase_delay)
        
        logger.debug(f"添加大气效应，衰减: {attenuation_db} dB, 相位延迟: {phase_delay:.3f} rad")
        return phase_shifted_signal
    
    def generate_realistic_sar_noise(self, clean_image: np.ndarray,
                                    noise_params: Optional[Dict] = None) -> np.ndarray:
        """
        生成真实的SAR图像噪声组合
        
        Args:
            clean_image: 干净的SAR图像
            noise_params: 噪声参数字典
            
        Returns:
            添加真实噪声的SAR图像
        """
        if noise_params is None:
            noise_params = {}
            
        noisy_image = clean_image.copy()
        
        # 1. 添加热噪声
        thermal_power = noise_params.get('thermal_noise_power', self.thermal_noise_power)
        noisy_image = self.add_thermal_noise(noisy_image, thermal_power)
        
        # 2. 添加杂波
        clutter_ratio = noise_params.get('clutter_ratio', self.clutter_ratio)
        noisy_image = self.add_clutter_noise(noisy_image, clutter_ratio)
        
        # 3. 添加相位噪声
        phase_std = noise_params.get('phase_noise_std', 0.05)
        noisy_image = self.add_phase_noise(noisy_image, phase_std)
        
        # 4. 添加大气效应
        attenuation = noise_params.get('atmospheric_attenuation', 0.1)
        phase_delay = noise_params.get('atmospheric_phase_delay', 0.0)
        noisy_image = self.add_atmospheric_effects(noisy_image, attenuation, phase_delay)
        
        # 5. 转换为幅度图像并添加斑点噪声
        magnitude_image = np.abs(noisy_image)
        speckle_var = noise_params.get('speckle_variance', self.speckle_variance)
        final_image = self.add_speckle_noise(magnitude_image, speckle_var)
        
        # 6. 量化噪声（可选）
        if noise_params.get('add_quantization', False):
            bits = noise_params.get('quantization_bits', 8)
            final_image = np.abs(self.add_quantization_noise(
                final_image.astype(complex), bits
            ))
        
        logger.info("生成真实SAR噪声完成")
        return final_image
    
    def calculate_noise_statistics(self, noisy_image: np.ndarray,
                                  clean_image: Optional[np.ndarray] = None) -> Dict:
        """
        计算噪声统计特性
        
        Args:
            noisy_image: 含噪声图像
            clean_image: 干净图像（可选）
            
        Returns:
            噪声统计字典
        """
        stats = {
            'mean': np.mean(noisy_image),
            'std': np.std(noisy_image),
            'variance': np.var(noisy_image),
            'min': np.min(noisy_image),
            'max': np.max(noisy_image),
            'dynamic_range_db': 20 * np.log10(np.max(noisy_image) / np.min(noisy_image[noisy_image > 0]))
        }
        
        if clean_image is not None:
            # 计算信噪比
            signal_power = np.mean(clean_image**2)
            noise_power = np.mean((noisy_image - clean_image)**2)
            stats['snr_db'] = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else np.inf
            
            # 计算均方误差
            stats['mse'] = np.mean((noisy_image - clean_image)**2)
            stats['rmse'] = np.sqrt(stats['mse'])
            
            # 计算峰值信噪比
            max_value = np.max(clean_image)
            stats['psnr_db'] = 20 * np.log10(max_value / np.sqrt(stats['mse'])) if stats['mse'] > 0 else np.inf
        
        return stats
