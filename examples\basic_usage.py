#!/usr/bin/env python3
"""
SAR仿真项目基本使用示例
"""
import os
import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils import config, setup_logger, blender_runner
from batch import BatchGenerator


def basic_dataset_generation():
    """基本数据集生成示例"""
    logger = setup_logger("basic_example")
    
    logger.info("开始基本数据集生成示例")
    
    try:
        # 验证配置
        logger.info("验证配置...")
        config_valid = config.validate_config()
        logger.info(f"配置验证结果: {config_valid}")

        # 在测试环境中，即使配置验证失败也继续演示
        if not config_valid:
            logger.warning("配置验证失败，但在演示模式下继续")

        # 检查Blender是否可用
        logger.info("检查Blender可用性...")
        if not blender_runner.is_blender_available():
            logger.error(f"Blender不可用，路径: {blender_runner.blender_path}")
            logger.error("请检查config.yaml中的blender.executable_path配置")
            return False

        logger.info(f"Blender可用: {blender_runner.blender_path}")

        # 使用Blender生成数据集
        logger.info("开始在Blender中生成演示数据集（2张图像）...")

        success = blender_runner.run_dataset_generation(num_images=2)

        if success:
            logger.info("演示数据集生成成功！")
            return True
        else:
            logger.error("数据集生成失败")
            return False
            
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        return False


def test_individual_components():
    """测试各个组件的基本功能"""
    logger = setup_logger("component_test")
    
    logger.info("开始测试各个组件...")
    
    try:
        # 测试配置加载
        logger.info("测试配置加载...")
        aircraft_models = config.get('aircraft.models')
        logger.info(f"找到 {len(aircraft_models)} 个飞机模型配置")
        
        # 测试SAR参数
        logger.info("测试SAR参数...")
        sar_frequency = config.get('sar.frequency')
        logger.info(f"SAR频率: {sar_frequency/1e9:.2f} GHz")
        
        # 测试雷达仿真器
        logger.info("测试雷达仿真器...")
        from sar import RadarSimulator
        radar = RadarSimulator()
        radar_params = radar.get_radar_parameters()
        logger.info(f"雷达波长: {radar_params['wavelength']*100:.2f} cm")
        
        # 测试RCS计算器
        logger.info("测试RCS计算器...")
        from sar import RCSCalculator
        rcs_calc = RCSCalculator()
        typical_rcs = rcs_calc.get_typical_rcs_values()
        logger.info(f"典型RCS值: {list(typical_rcs.keys())}")
        
        # 测试噪声生成器
        logger.info("测试噪声生成器...")
        from sar import NoiseGenerator
        import numpy as np
        
        noise_gen = NoiseGenerator()
        test_signal = np.ones((64, 64), dtype=complex)
        noisy_signal = noise_gen.add_thermal_noise(test_signal)
        logger.info(f"噪声添加测试完成，信号尺寸: {noisy_signal.shape}")
        
        # 测试COCO生成器
        logger.info("测试COCO生成器...")
        from dataset import COCOGenerator
        coco_gen = COCOGenerator()
        categories = coco_gen.categories
        logger.info(f"COCO类别数: {len(categories)}")
        
        logger.info("所有组件测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"组件测试失败: {e}")
        return False


def demonstrate_sar_physics():
    """演示SAR物理原理"""
    logger = setup_logger("sar_physics")
    
    logger.info("演示SAR物理原理...")
    
    try:
        from sar import RadarSimulator
        import numpy as np
        import matplotlib.pyplot as plt
        
        radar = RadarSimulator()
        
        # 演示多普勒效应
        logger.info("演示多普勒效应...")
        velocities = np.linspace(-200, 200, 41)  # -200 to 200 m/s
        doppler_freqs = [radar.calculate_doppler_frequency(v) for v in velocities]
        
        # 保存多普勒频移图
        plt.figure(figsize=(10, 6))
        plt.plot(velocities, doppler_freqs)
        plt.xlabel('相对速度 (m/s)')
        plt.ylabel('多普勒频移 (Hz)')
        plt.title('多普勒频移 vs 相对速度')
        plt.grid(True)
        plt.savefig('doppler_effect.png')
        logger.info("多普勒效应图已保存为 doppler_effect.png")
        
        # 演示路径损耗
        logger.info("演示路径损耗...")
        distances = np.logspace(2, 5, 100)  # 100m to 100km
        path_losses = [radar.calculate_path_loss(d) for d in distances]
        
        plt.figure(figsize=(10, 6))
        plt.semilogx(distances, path_losses)
        plt.xlabel('距离 (m)')
        plt.ylabel('路径损耗 (dB)')
        plt.title('自由空间路径损耗')
        plt.grid(True)
        plt.savefig('path_loss.png')
        logger.info("路径损耗图已保存为 path_loss.png")
        
        # 演示雷达方程
        logger.info("演示雷达方程...")
        rcs_values = [0.1, 1.0, 10.0, 100.0]  # 不同RCS值
        distance = 10000  # 10km
        
        for rcs in rcs_values:
            power = radar.calculate_radar_equation(rcs, distance)
            power_db = 10 * np.log10(power) if power > 0 else -np.inf
            logger.info(f"RCS {rcs} m², 距离 {distance/1000} km: 接收功率 {power_db:.2f} dB")
        
        logger.info("SAR物理原理演示完成！")
        return True
        
    except Exception as e:
        logger.error(f"SAR物理演示失败: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="SAR仿真项目使用示例")
    parser.add_argument("--demo", choices=['basic', 'components', 'physics', 'all'], 
                       default='basic', help="选择演示类型")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志级别
    log_level = logging.DEBUG if args.verbose else logging.INFO
    
    success = True
    
    if args.demo in ['basic', 'all']:
        print("=" * 50)
        print("基本数据集生成示例")
        print("=" * 50)
        success &= basic_dataset_generation()
    
    if args.demo in ['components', 'all']:
        print("\n" + "=" * 50)
        print("组件功能测试")
        print("=" * 50)
        success &= test_individual_components()
    
    if args.demo in ['physics', 'all']:
        print("\n" + "=" * 50)
        print("SAR物理原理演示")
        print("=" * 50)
        success &= demonstrate_sar_physics()
    
    if success:
        print("\n✅ 所有示例执行成功！")
        return 0
    else:
        print("\n❌ 部分示例执行失败，请检查日志。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
