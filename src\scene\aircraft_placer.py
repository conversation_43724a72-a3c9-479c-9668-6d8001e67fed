"""
飞机放置器
"""
import random
import math
from typing import List, Dict, Tuple, Optional
import logging

try:
    import bpy
    from mathutils import Vector, Euler
    BLENDER_AVAILABLE = True
except ImportError:
    BLENDER_AVAILABLE = False
    class MockObject:
        def __init__(self, name):
            self.name = name
            self.type = 'MESH'
            self.location = (0, 0, 0)
            self.rotation_euler = (0, 0, 0)
    bpy = None

try:
    from ..utils import config
    from ..models import AircraftLoader, ModelNormalizer, MaterialManager
except ImportError:
    # 处理测试环境中的导入问题
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils import config
    from models import AircraftLoader, ModelNormalizer, MaterialManager

logger = logging.getLogger(__name__)


class AircraftPlacer:
    """飞机放置器，负责在场景中随机放置飞机"""
    
    def __init__(self):
        """初始化飞机放置器"""
        self.aircraft_loader = AircraftLoader()
        self.model_normalizer = ModelNormalizer()
        self.material_manager = MaterialManager()
        
        # 从配置获取参数
        self.aircraft_count_range = (
            config.get('scene.aircraft_count.min', 3),
            config.get('scene.aircraft_count.max', 15)
        )
        
        self.placement_config = config.get('scene.placement', {})
        self.height_range = self.placement_config.get('height_range', [0, 50])
        self.pitch_range = self.placement_config.get('pitch_range', [-15, 15])
        self.yaw_range = self.placement_config.get('yaw_range', [0, 360])
        self.roll_range = self.placement_config.get('roll_range', [-10, 10])
        self.min_distance = self.placement_config.get('min_distance', 30)
        
        # 设置默认地面边界，防止初始化问题
        default_ground_size = config.get('scene.ground_size', [1000, 1000])
        half_x = default_ground_size[0] / 2
        half_y = default_ground_size[1] / 2
        self.ground_bounds = (-half_x, half_x, -half_y, half_y)  # 默认边界
        
    def set_ground_bounds(self, bounds: Tuple[float, float, float, float]):
        """
        设置地面边界
        
        Args:
            bounds: (min_x, max_x, min_y, max_y)
        """
        self.ground_bounds = bounds
        
    def generate_aircraft_placement_plan(self, seed: Optional[int] = None) -> List[Dict]:
        """
        生成飞机放置计划
        
        Args:
            seed: 随机种子
            
        Returns:
            放置计划列表，每个元素包含飞机信息和位置参数
        """
        if seed is not None:
            random.seed(seed)
            
        # 确定飞机数量
        aircraft_count = random.randint(*self.aircraft_count_range)
        
        # 获取可用的飞机模型
        available_models = self.aircraft_loader.list_available_models()
        logger.info(f"可用飞机模型: {available_models}")

        if not available_models:
            logger.error("没有可用的飞机模型，将使用默认模型列表")
            # 使用默认模型列表作为fallback
            available_models = ['Boeing737', 'Boeing747', 'F16', 'F22', 'SR71', 'X35']
            logger.info(f"使用默认模型列表: {available_models}")

        if not available_models:
            logger.error("仍然没有可用的飞机模型")
            return []
            
        placement_plan = []
        placed_positions = []  # 记录已放置的位置
        
        for i in range(aircraft_count):
            # 随机选择飞机型号
            model_name = random.choice(available_models)
            model_config = self.aircraft_loader.get_model_config(model_name)

            # 如果没有找到配置，创建默认配置
            if not model_config:
                logger.warning(f"未找到模型配置 {model_name}，使用默认配置")
                model_config = {
                    'name': model_name,
                    'file': f'{model_name}.fbx',
                    'category_id': 1,
                    'length': 20.0
                }
            
            # 生成有效的放置位置
            position = self._generate_valid_position(placed_positions)
            if position is None:
                logger.warning(f"无法为第{i+1}架飞机找到有效位置，跳过")
                continue
                
            # 生成随机角度
            pitch = math.radians(random.uniform(*self.pitch_range))
            yaw = math.radians(random.uniform(*self.yaw_range))
            roll = math.radians(random.uniform(*self.roll_range))
            
            # 生成随机高度
            height = random.uniform(*self.height_range)
            
            # 创建放置计划
            plan = {
                'model_name': model_name,
                'model_config': model_config,
                'position': (position[0], position[1], height),
                'rotation': (pitch, yaw, roll),
                'instance_id': i + 1
            }
            
            placement_plan.append(plan)
            placed_positions.append(position)
            
        logger.info(f"生成放置计划: {len(placement_plan)}架飞机")
        return placement_plan
    
    def _generate_valid_position(self, existing_positions: List[Tuple[float, float]], 
                                max_attempts: int = 100) -> Optional[Tuple[float, float]]:
        """
        生成有效的放置位置（避免重叠）
        
        Args:
            existing_positions: 已存在的位置列表
            max_attempts: 最大尝试次数
            
        Returns:
            有效位置或None
        """
        if not self.ground_bounds:
            logger.error("地面边界未设置")
            return None
            
        min_x, max_x, min_y, max_y = self.ground_bounds
        
        # 考虑最小距离的边界缩减
        margin = self.min_distance
        safe_min_x = min_x + margin
        safe_max_x = max_x - margin
        safe_min_y = min_y + margin
        safe_max_y = max_y - margin
        
        if safe_min_x >= safe_max_x or safe_min_y >= safe_max_y:
            logger.error("地面区域太小，无法放置飞机")
            return None
            
        for attempt in range(max_attempts):
            # 生成随机位置
            x = random.uniform(safe_min_x, safe_max_x)
            y = random.uniform(safe_min_y, safe_max_y)
            
            # 检查与现有位置的距离
            valid = True
            for existing_pos in existing_positions:
                distance = math.sqrt((x - existing_pos[0])**2 + (y - existing_pos[1])**2)
                if distance < self.min_distance:
                    valid = False
                    break
                    
            if valid:
                return (x, y)
                
        logger.warning(f"在{max_attempts}次尝试后未找到有效位置")
        return None
    
    def place_aircraft_from_plan(self, placement_plan: List[Dict]):
        """
        根据放置计划在场景中放置飞机

        Args:
            placement_plan: 放置计划

        Returns:
            放置的飞机对象列表
        """
        placed_aircraft = []

        if not BLENDER_AVAILABLE:
            # 在测试环境中返回模拟对象
            for plan in placement_plan:
                mock_obj = MockObject(f"{plan['model_name']}_{plan['instance_id']:03d}")
                mock_obj.location = plan['position']
                mock_obj.rotation_euler = plan['rotation']
                placed_aircraft.append(mock_obj)
            return placed_aircraft

        for i, plan in enumerate(placement_plan):
            aircraft_obj = None
            try:
                # 尝试加载真实模型
                aircraft_obj = self.aircraft_loader.duplicate_model(plan['model_name'])
                if not aircraft_obj:
                    logger.warning(f"无法复制飞机模型: {plan['model_name']}，尝试直接加载")
                    aircraft_obj = self.aircraft_loader.load_model(plan['model_name'])

                if not aircraft_obj:
                    logger.warning(f"无法加载真实模型: {plan['model_name']}，创建fallback对象")
                    aircraft_obj = self.aircraft_loader._create_fallback_aircraft(plan['model_name'])

                # 如果还是失败，创建最简单的对象
                if not aircraft_obj:
                    logger.warning(f"fallback创建失败，创建最简单的飞机对象: {plan['model_name']}")
                    aircraft_obj = self._create_simple_aircraft(plan['model_name'], plan['instance_id'])

                if not aircraft_obj:
                    logger.error(f"完全无法创建飞机对象: {plan['model_name']}，跳过")
                    continue
                    
                # 重命名对象
                aircraft_obj.name = f"{plan['model_name']}_{plan['instance_id']:03d}"
                
                # 标准化模型
                if not self.model_normalizer.normalize_model(aircraft_obj):
                    logger.error(f"模型标准化失败: {aircraft_obj.name}")
                    continue
                    
                # 设置材质
                if not self.material_manager.setup_aircraft_material(aircraft_obj):
                    logger.warning(f"设置材质失败: {aircraft_obj.name}")
                    
                # 设置位置
                aircraft_obj.location = plan['position']
                
                # 设置旋转
                aircraft_obj.rotation_euler = plan['rotation']
                
                # 添加自定义属性用于数据集生成
                aircraft_obj['aircraft_type'] = plan['model_name']
                aircraft_obj['category_id'] = plan['model_config']['category_id']
                aircraft_obj['instance_id'] = plan['instance_id']
                
                placed_aircraft.append(aircraft_obj)
                logger.info(f"放置飞机: {aircraft_obj.name} at {plan['position']}")
                
            except Exception as e:
                logger.error(f"放置飞机失败 {plan['model_name']}: {e}")
                continue
                
        logger.info(f"成功放置 {len(placed_aircraft)} 架飞机")
        return placed_aircraft
    
    def get_aircraft_info(self, aircraft_objects) -> List[Dict]:
        """
        获取已放置飞机的信息
        
        Args:
            aircraft_objects: 飞机对象列表
            
        Returns:
            飞机信息列表
        """
        aircraft_info = []
        
        for obj in aircraft_objects:
            if not obj or (hasattr(obj, 'type') and obj.type != 'MESH'):
                continue

            if not BLENDER_AVAILABLE:
                # 在测试环境中返回模拟信息
                info = {
                    'name': obj.name,
                    'aircraft_type': 'F16',
                    'category_id': 1,
                    'instance_id': 1,
                    'location': list(obj.location),
                    'rotation': list(obj.rotation_euler),
                    'bbox_world': {
                        'min_x': -10, 'max_x': 10,
                        'min_y': -10, 'max_y': 10,
                        'min_z': 0, 'max_z': 5
                    },
                    'dimensions': {
                        'length': 20, 'width': 15, 'height': 5
                    }
                }
                aircraft_info.append(info)
                continue

            # 获取世界坐标下的边界框
            bbox = [obj.matrix_world @ Vector(corner) for corner in obj.bound_box]
            
            # 计算边界框
            min_x = min(v.x for v in bbox)
            max_x = max(v.x for v in bbox)
            min_y = min(v.y for v in bbox)
            max_y = max(v.y for v in bbox)
            min_z = min(v.z for v in bbox)
            max_z = max(v.z for v in bbox)
            
            info = {
                'name': obj.name,
                'aircraft_type': obj.get('aircraft_type', 'unknown'),
                'category_id': obj.get('category_id', 0),
                'instance_id': obj.get('instance_id', 0),
                'location': list(obj.location),
                'rotation': list(obj.rotation_euler),
                'bbox_world': {
                    'min_x': min_x, 'max_x': max_x,
                    'min_y': min_y, 'max_y': max_y,
                    'min_z': min_z, 'max_z': max_z
                },
                'dimensions': {
                    'length': max_x - min_x,
                    'width': max_y - min_y,
                    'height': max_z - min_z
                }
            }
            
            aircraft_info.append(info)
            
        return aircraft_info

    def _create_simple_aircraft(self, model_name: str, instance_id: int):
        """
        创建最简单的飞机对象（当所有其他方法都失败时）

        Args:
            model_name: 模型名称
            instance_id: 实例ID

        Returns:
            简单的飞机对象
        """
        if not BLENDER_AVAILABLE:
            # 在非Blender环境中创建MockObject
            obj = MockObject(f"SimpleAircraft_{model_name}_{instance_id}")
            obj.location = [0, 0, 0]
            obj.rotation_euler = [0, 0, 0]
            obj.scale = [1, 1, 1]
            return obj

        try:
            import bpy
            from mathutils import Vector

            # 确保在正确的模式下
            if bpy.context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # 清除选择
            bpy.ops.object.select_all(action='DESELECT')

            # 创建一个简单的立方体
            bpy.ops.mesh.primitive_cube_add(size=1, location=(0, 0, 0))
            aircraft_obj = bpy.context.active_object

            if aircraft_obj:
                aircraft_obj.name = f"SimpleAircraft_{model_name}_{instance_id}"
                aircraft_obj.scale = (2, 1, 0.3)  # 飞机形状

                # 设置属性
                aircraft_obj['aircraft_type'] = model_name
                aircraft_obj['category_id'] = 1
                aircraft_obj['instance_id'] = instance_id
                aircraft_obj['is_simple_fallback'] = True

                logger.info(f"创建简单飞机对象: {aircraft_obj.name}")
                return aircraft_obj

        except Exception as e:
            logger.error(f"创建简单飞机对象失败: {e}")

        return None

    def validate_placement(self, aircraft_objects) -> bool:
        """
        验证飞机放置是否有效（无重叠等）
        
        Args:
            aircraft_objects: 飞机对象列表
            
        Returns:
            是否有效
        """
        if len(aircraft_objects) < 2:
            return True
            
        # 检查飞机间距离
        for i, obj1 in enumerate(aircraft_objects):
            for j, obj2 in enumerate(aircraft_objects[i+1:], i+1):
                if BLENDER_AVAILABLE:
                    distance = (Vector(obj1.location) - Vector(obj2.location)).length
                else:
                    # 在测试环境中计算距离
                    dx = obj1.location[0] - obj2.location[0]
                    dy = obj1.location[1] - obj2.location[1]
                    dz = obj1.location[2] - obj2.location[2]
                    distance = (dx**2 + dy**2 + dz**2)**0.5

                if distance < self.min_distance:
                    logger.warning(f"飞机距离过近: {obj1.name} 和 {obj2.name}, 距离: {distance:.2f}m")
                    return False
                    
        return True
    
    def clear_aircraft(self):
        """清除场景中的所有飞机"""
        if not BLENDER_AVAILABLE:
            logger.info("清除了飞机（模拟模式）")
            return

        # 查找并删除所有飞机对象
        aircraft_objects = [obj for obj in bpy.context.scene.objects
                          if obj.type == 'MESH' and 'aircraft_type' in obj]

        for obj in aircraft_objects:
            bpy.data.objects.remove(obj, do_unlink=True)

        logger.info(f"清除了 {len(aircraft_objects)} 架飞机")

    def optimize_placement(self, placement_plan: List[Dict]) -> List[Dict]:
        """
        优化飞机放置计划，避免重叠和提高分布均匀性

        Args:
            placement_plan: 原始放置计划

        Returns:
            优化后的放置计划
        """
        if not placement_plan:
            return placement_plan

        optimized_plan = []
        used_positions = []

        # 按优先级排序（可以根据飞机大小、重要性等）
        sorted_plan = sorted(placement_plan, key=lambda x: x['instance_id'])

        for plan in sorted_plan:
            # 检查当前位置是否有效
            current_pos = (plan['position'][0], plan['position'][1])

            if self._is_position_valid(current_pos, used_positions):
                optimized_plan.append(plan)
                used_positions.append(current_pos)
            else:
                # 尝试找到新的有效位置
                new_pos = self._generate_valid_position(used_positions)
                if new_pos:
                    plan['position'] = (new_pos[0], new_pos[1], plan['position'][2])
                    optimized_plan.append(plan)
                    used_positions.append(new_pos)
                else:
                    logger.warning(f"无法为飞机 {plan['model_name']} 找到有效位置，跳过")

        logger.info(f"放置计划优化完成: {len(optimized_plan)}/{len(placement_plan)} 架飞机")
        return optimized_plan

    def _is_position_valid(self, position: Tuple[float, float],
                          existing_positions: List[Tuple[float, float]]) -> bool:
        """
        检查位置是否有效

        Args:
            position: 要检查的位置
            existing_positions: 已存在的位置列表

        Returns:
            是否有效
        """
        for existing_pos in existing_positions:
            distance = math.sqrt((position[0] - existing_pos[0])**2 +
                               (position[1] - existing_pos[1])**2)
            if distance < self.min_distance:
                return False
        return True
